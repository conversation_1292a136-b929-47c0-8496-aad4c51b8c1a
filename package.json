{"name": "soybean-admin", "type": "module", "version": "1.3.11", "description": "A fresh and elegant admin template, based on Vue3、Vite3、TypeScript、NaiveUI and UnoCSS. 一个基于Vue3、Vite3、TypeScript、NaiveUI and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Soybean", "email": "<EMAIL>", "url": "https://github.com/soybeanjs"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite5", "TypeScript", "naive-ui", "naive-ui-admin", "ant-design-vue v4", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode dev", "dev:test": "vite --mode test", "dev:prod": "vite --mode prod", "gen-route": "sa gen-route", "lint": "eslint . --fix", "preview": "vite preview", "release": "sa release", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify/vue": "^4.3.0", "@province-city-china/level": "^8.5.8", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@tinymce/tinymce-vue": "^6.1.0", "@vueuse/core": "12.4.0", "clipboard": "2.0.11", "dayjs": "1.11.13", "decimal.js": "^10.5.0", "defu": "6.1.4", "echarts": "5.6.0", "json5": "2.2.3", "naive-ui": "2.41.0", "nprogress": "0.2.0", "pinia": "2.3.0", "sortablejs": "^1.15.6", "tailwind-merge": "2.6.0", "tinymce": "^7.6.1", "uuid": "^11.0.5", "vue": "3.5.13", "vue-draggable-plus": "0.6.0", "vue-i18n": "11.0.1", "vue-router": "4.5.0", "vuedraggable": "4.1.0"}, "devDependencies": {"@elegant-router/vue": "0.3.8", "@iconify/json": "^2.2.322", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.4.4", "@types/node": "22.10.7", "@types/nprogress": "0.2.3", "@unocss/eslint-config": "65.4.2", "@unocss/preset-icons": "65.4.2", "@unocss/preset-uno": "65.4.2", "@unocss/transformer-directives": "65.4.2", "@unocss/transformer-variant-group": "65.4.2", "@unocss/vite": "65.4.2", "@vicons/antd": "^0.13.0", "@vicons/ionicons5": "^0.13.0", "@vicons/material": "^0.13.0", "@vitejs/plugin-vue": "5.2.1", "@vitejs/plugin-vue-jsx": "4.1.1", "eslint": "9.18.0", "eslint-plugin-vue": "9.32.0", "lint-staged": "15.4.1", "sass": "1.83.4", "tsx": "4.19.2", "typescript": "5.7.3", "unplugin-icons": "22.0.0", "unplugin-vue-components": "28.0.0", "vite": "6.0.7", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.7.0", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.2.0"}}