import { request } from '../request';

/** 管理员 */
export function fetchAdminList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Manage.Admin>>({
    url: '/admins',
    method: 'get',
    params
  });
}

export function searchAdmin() {
  return request<Api.Manage.Admin[]>({
    url: `/admins/search`,
    method: 'get'
  });
}

export function getAdmin(id: number) {
  return request<Api.Manage.Admin>({
    url: `/admins/${id}`,
    method: 'get'
  });
}


export function createAdmin(data: object) {
  return request<Api.Manage.Admin>({
    url: '/admins',
    method: 'post',
    data
  });
}

export function updateAdmin(id: number, data: object) {
  return request<Api.Manage.Admin>({
    url: '/admins/' + id,
    method: 'put',
    data
  });
}

export function updateAdminPassword(id: number, data: object) {
  return request<Api.Manage.Admin>({
    url: `/admins/${id}/updatePassword`,
    method: 'put',
    data
  });
}

export function fetchGetAllRoles(params?: object) {
  return request<Api.Manage.Role[]>({
    url: '/roles/search',
    method: 'get',
    params
  });
}
