import { request } from '../request';

export function getUploadConfig(data?: object) {
  return request<Api.System.UploadConfig>({
    url: '/system/upload/config',
    method: 'post',
    data
  });
}

export function uploadFile(url: string, data: object, params?: any) {
  return request<Api.System.UploaderFile>({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    params,
    data
  });
}

export function fetchTemplateList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.Template>>({
    url: `/templates`,
    method: 'get',
    params
  });
}

export function createTemplate(data: object) {
  return request<Api.Org.Template>({
    url: `/templates`,
    method: 'post',
    data
  });
}

export function updateTemplate(id: number, data: object) {
  return request<Api.Org.Template>({
    url: `/templates/${id}`,
    method: 'put',
    data
  });
}

export function setDefaultTemplate(id: number) {
  return request<Api.Org.Template>({
    url: `/templates/default/${id}`,
    method: 'put'
  });
}

export function removeTemplate(id: number) {
  return request({
    url: `/templates/${id}`,
    method: 'delete'
  });
}

export function getTemplateFields() {
  return request({
    url: `/templates/getFields`,
    method: 'get'
  });
}
