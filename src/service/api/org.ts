import { request } from '@/service/request';

export function getOrg() {
  return request<Api.Org.Org>({
    url: `/orgs`,
    method: 'get'
  });
}

export function updateOrg(data: object) {
  return request<Api.Org.Org>({
    url: '/orgs',
    method: 'put',
    data
  });
}

export function fetchBalanceRecordList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.BalanceRecord>>({
    url: `/balanceRecords`,
    method: 'get',
    params
  });
}

export function fetchTopicList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.Topic>>({
    url: `/topics`,
    method: 'get',
    params
  });
}

export function updateTopic(id: number, data: object) {
  return request<Api.Org.Topic>({
    url: `/topics/${id}`,
    method: 'put',
    data
  });
}

export function getTopicSubjectTypeCount(params?: object) {
  return request<Api.Train.TopicSubjectTypeCount>({
    url: `/topics/getSubjectTypeCount`,
    method: 'get',
    params
  });
}

export function batchUpdateTopic(data: object) {
  return request({
    url: '/topics/batchUpdate',
    method: 'put',
    data
  });
}

export function fetchCourseList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.Course>>({
    url: `/courses`,
    method: 'get',
    params
  });
}


export function batchUpdateCourse(data: object) {
  return request({
    url: '/courses/batchUpdate',
    method: 'put',
    data
  });
}

export function getCourseChapterSections(id: number) {
  return request<Api.Cms.ChapterSection[]>({
    url: `courses/${id}/getChapterSections`,
    method: 'get'
  });
}

export function operateCourseSub(id: number, data: object) {
  return request({
    url: `courses/${id}/operate`,
    method: 'post',
    data
  });
}

export function fetchCoursePackList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.CoursePack>>({
    url: `/coursePacks`,
    method: 'get',
    params
  });
}


export function batchUpdateCoursePack(data: object) {
  return request({
    url: '/coursePacks/batchUpdate',
    method: 'put',
    data
  });
}

export function fetchClassList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.Class>>({
    url: `/classes`,
    method: 'get',
    params
  });
}

export function getClassOptions() {
  return request<Api.Org.ClassOption>({
    url: `/classes/getOptions`,
    method: 'get'
  });
}

export function getClass(id: number) {
  return request<Api.Org.Class>({
    url: `/classes/${id}`,
    method: 'get'
  });
}

export function createClass(data: object) {
  return request<Api.Org.Class>({
    url: `/classes`,
    method: 'post',
    data
  });
}

export function updateClass(id: number, data: object) {
  return request<Api.Org.Class>({
    url: `/classes/${id}`,
    method: 'put',
    data
  });
}

export function removeClass(id: number) {
  return request({
    url: `/classes/${id}`,
    method: 'delete'
  });
}

export function removeStudent(id: number) {
  return request({
    url: `/students/${id}`,
    method: 'delete'
  });
}

export function finishedClass(id: number) {
  return request<Api.Org.Class>({
    url: `/classes/finished`,
    method: 'post',
    data: { id }
  });
}

export function fetchStudentList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.Student>>({
    url: `/students`,
    method: 'get',
    params
  });
}


export function updateStudent(id: number, data: object) {
  return request<Api.Org.Student>({
    url: `/students/${id}`,
    method: 'put',
    data
  });
}

export function batchAssignStudentSubject(data: object) {
  return request({
    url: `/students/batchAssign`,
    method: 'post',
    data
  });
}

export function fetchEnrollmentList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.Enrollment>>({
    url: `/enrollments`,
    method: 'get',
    params
  });
}

export function getEnrollmentOptions() {
  return request<Api.Org.EnrollmentOption>({
    url: `/enrollments/getOptions`,
    method: 'get'
  });
}

export function removeEnrollment(id: number) {
  return request({
    url: `/enrollments/${id}`,
    method: 'delete'
  });
}

export function examRetakeEnrollment(id: number) {
  return request<Api.Org.Enrollment>({
    url: `/enrollments/examRetake/${id}`,
    method: 'put'
  });
}

export function getIdCardInfoEnrollment(data: object) {
  return request({
    url: `/enrollments/getIdCardInfo`,
    method: 'post',
    data
  });
}

export function batchAssignEnrollmentSubject(data: object) {
  return request({
    url: `/enrollments/batchAssign`,
    method: 'post',
    data
  });
}

export function batchRemoveEnrollmentSubject(data: object) {
  return request({
    url: `/enrollments/batchRemove`,
    method: 'post',
    data
  });
}

export function getEnrollmentTestPaper(id: number) {
  return request<Api.Org.EnrollmentTestPaper>({
    url: '/enrollments/getTestPaper',
    method: 'post',
    data: { id }
  });
}

export function getEnrollmentHourCert(id: number) {
  return request<{image_url: string}>({
    url: '/enrollments/hourCertImage',
    method: 'post',
    data: { id }
  });
}

export function getEnrollmentForm() {
  return request<Api.Org.EnrollmentForm>({
    url: `/enrollmentForms`,
    method: 'get'
  });
}

export function updateEnrollmentForm(data: object) {
  return request<Api.Org.EnrollmentForm>({
    url: '/enrollmentForms',
    method: 'put',
    data
  });
}



/**
 * 导出数据
 *
 * @param data
 * @param data.type 导出类型
 * @param data.extra 额外参数
 * @returns
 */
export function createExport(data: { type: Api.Common.ExportType; extra?: object; checked_ids?: number[] }) {
  return request({
    url: '/exports',
    method: 'post',
    data
  });
}

/**
 * 导出记录列表
 *
 * @param params
 * @returns
 */
export function fetchExportList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.Export>>({
    url: `/exports`,
    method: 'get',
    params
  });
}

/**
 * 下载导出文件，返回文件地址
 *
 * @param id
 * @returns
 */
export function downloadExport(id: number) {
  return request<Api.Org.Download>({
    url: `/exports/${id}/download`,
    method: 'get'
  });
}

/**
 * 获取订单列表
 *
 * @param params
 * @returns
 */
export function fetchOrderList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.Order>>({
    url: `/orders`,
    method: 'get',
    params
  });
}

/**
 * 删除导出记录
 *
 * @param id
 * @returns
 */
export function removeExport(id: number) {
  return request({
    url: `/exports/${id}`,
    method: 'delete'
  });
}

/**
 * 导入学员信息
 *
 * @param file
 * @returns
 */
export function importEnrollment(file: File) {
  return request({
    url: `/enrollments/importEnrollment`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: { file }
  });
}

/**
 * 导入学员照片
 *
 * @param file
 * @returns
 */
export function importEnrollmentPhoto(file: File) {
  return request({
    url: `/enrollments/importEnrollmentPhoto`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: { file }
  });
}

/**
 * 导入学员身份证
 *
 * @param file
 * @returns
 */
export function importIdCardPhoto(file: File) {
  return request({
    url: `/students/importIdCardPhoto`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: { file }
  });
}

// 报名课程配置相关接口
export function fetchEnrollConfigList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.EnrollConfig>>({
    url: `/enroll-config`,
    method: 'get',
    params
  });
}

export function createEnrollConfig(data: object) {
  return request<Api.Org.EnrollConfig>({
    url: `/enroll-config`,
    method: 'post',
    data
  });
}

export function updateEnrollConfig(id: number, data: object) {
  return request<Api.Org.EnrollConfig>({
    url: `/enroll-config/${id}`,
    method: 'put',
    data
  });
}

export function removeEnrollConfig(id: number) {
  return request({
    url: `/enroll-config/${id}`,
    method: 'delete'
  });
}

/**
 * 获取报名记录列表
 *
 * @param params
 * @returns
 */
export function fetchEnrollRecordList(params: object) {
  return request<Api.Common.PaginatingQueryRecord<Api.Org.EnrollRecord>>({
    url: `/enroll`,
    method: 'get',
    params
  });
}

/**
 * 获取报名记录详情
 *
 * @param id
 * @returns
 */
export function getEnrollRecord(id: number) {
  return request<Api.Org.EnrollRecord>({
    url: `/enroll/${id}`,
    method: 'get'
  });
}

/**
 * 审核报名记录
 *
 * @param id
 * @param data
 * @returns
 */
export function auditEnrollRecord(id: number, data: { status: number; remark?: string }) {
  return request<Api.Org.EnrollRecord>({
    url: `/enroll/${id}/audit`,
    method: 'put',
    data
  });
}
