<script setup lang="ts">
import { $t } from '@/locales';
import { enableStatusOptions } from '@/constants/business';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type SearchParams = {
  keyword?: string;
  status?: number;
}

const emit = defineEmits<Emits>();


const model = defineModel<SearchParams>('model', { required: true });

async function reset() {
  emit('reset');
}

async function search() {
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse :default-expanded-names="['search']">
      <NCollapseItem :title="$t('common.search')" name="search">
        <NForm :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:4" label="关键词" class="pr-24px">
              <NInput v-model:value="model.keyword" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:4" label="状态" class="pr-24px">
              <NSelect v-model:value="model.status" :options="enableStatusOptions" clearable/>
            </NFormItemGi>

            <NFormItemGi span="24 s:12 m:4" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
