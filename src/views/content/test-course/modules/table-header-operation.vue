<script setup lang="ts">

interface Props {
  loading?: boolean;
  disabled?: boolean;
}

defineProps<Props>();

interface Emits {
  (e: 'updateStatus', status: number): void;
  (e: 'updatePrice'): void;
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<NaiveUI.TableColumnCheck[]>('columns', {
  default: () => []
});

function updateStatus(status: number) {
  emit('updateStatus', status);
}

function updatePrice() {
  emit('updatePrice');
}

function refresh() {
  emit('refresh');
}
</script>

<template>
  <NSpace wrap justify="end" class="lt-sm:w-200px">
    <slot name="default">
      <NButton size="small" type="primary" :disabled="disabled" @click="updateStatus(1)">
        上线
      </NButton>
      <NButton size="small" type="error" :disabled="disabled" @click="updateStatus(0)">
        下线
      </NButton>
      <NButton size="small" type="success" :disabled="disabled" @click="updatePrice">
        调价
      </NButton>
    </slot>
    <TableColumnSetting v-model:columns="columns" />
    <slot name="suffix"></slot>
  </NSpace>
</template>

<style scoped></style>
