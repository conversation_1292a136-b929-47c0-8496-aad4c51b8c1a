<script setup lang="tsx">
import type { DataTableColumns } from 'naive-ui';
import { ref } from 'vue';
import { getCourseChapterSections } from "@/service/api";
import TableSectionSwitch from "./table-section-switch.vue";
import Decimal from 'decimal.js';
interface Emits {
  (e: 'reset'): void;
}

const props = defineProps<{
  course: Api.Org.Course | null;
}>();
const emit = defineEmits<Emits>();

const columns: DataTableColumns = [
  {
    key: 'name_desc',
    title: '章节',
    align: 'left',
  },
  {
    key: 'hour',
    title: '学时',
    align: 'center',
  },
  {
    key: 'status',
    title: '状态',
    align: 'center',
    render: row => {
      return <TableSectionSwitch v-model:active={row.status} rowData={row as Api.Cms.ChapterSection} onReload={() => handleReload()} />
    }
  },
];

const visible = ref<boolean>(false);
const loading = ref<boolean>(false);
const tableList = ref<Api.Cms.ChapterSection[]>([]);
const totalHour = ref<number>(0);
const passedHour = ref<number>(0);

async function init() {
  loading.value = true;

  const { error, data } = await getCourseChapterSections(props.course?.course_id);

  loading.value = false;

  if (error) {
    return;
  }

  let total = 0;
  let totalDuration = 0;
  data.forEach(item => {
    if (item.status) {
      item.children.forEach(items => {
        if (items.status) {
          totalDuration += parseFloat(items.duration);
        }
      })
    }
  });
  let duration = (totalDuration / (props.course.content_course.hour_per_minutes * 60));
  totalHour.value = new Decimal(duration).toDecimalPlaces(1, Decimal.ROUND_FLOOR).toNumber();
  passedHour.value = Math.floor(totalHour.value);
  tableList.value = data;
}

function handleReload() {
  init();

  emit('reset');
}

async function open() {
  tableList.value = [];
  visible.value = true;

  await init();
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="860">
    <NDrawerContent title="课程列表" :native-scrollbar="false" closable>
      <NSpace v-if="course" size="large">
        <NText class="course-txt">{{ course?.content_course?.content?.title }}</NText>
        <NText class="course-txt">总学时：{{ totalHour }}</NText>
        <NText class="course-txt">合格学时：{{ Math.floor(totalHour) }}</NText>
      </NSpace>

      <NDataTable
        v-if="tableList.length > 0"
        :columns="columns"
        :data="tableList"
        :default-expand-all="true"
        :row-key="row => row.id"
        :indent="32"
        :loading="loading"
        remote
        size="small"
        class="mt-3"
      />
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
  .course-txt {
    font-size: 18px;
    font-weight: bold;
    margin-right: 24px;
  }
</style>
