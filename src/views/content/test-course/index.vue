<script setup lang="tsx">
import {nextTick, ref} from 'vue';
import { DataTableRowData, DataTableRowKey, NButton, NTag } from 'naive-ui';
import { fetchCourseList, batchUpdateCourse } from '@/service/api';
import { enableStatusRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import DateFormat from '@/components/common/date/date-format.vue';
import TableHeaderOperation from './modules/table-header-operation.vue';
import TableOperate from './modules/table-operate.vue';
import TableSearch from './modules/table-search.vue';
import TableSection from './modules/table-sections.vue';


const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchCourseList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    status: null,
    keyword: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'id',
      title: 'ID',
      align: 'center'
    },
    {
      key: 'course_id',
      title: '课程ID',
      align: 'center'
    },
    {
      key: 'content_course',
      title: '课程名称',
      align: 'center',
      render: (row) => {
        return row.content_course?.content?.title ?? '-';
      }
    },
    {
      key: 'hour',
      title: '学时',
      align: 'center'
    },
    {
      key: 'price_original',
      title: '原价',
      align: 'center'
    },
    {
      key: 'price_sell',
      title: '售价',
      align: 'center'
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'error'
        };

        const label = enableStatusRecord[row.status];

        return (
          <NTag type={tagMap[row.status]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      render: (row, index) => (
        <NButton type="primary" size="small" onClick={() => showSection(row, index)}>
          章节管理
        </NButton>
      )
    }
  ]
});

const maxPrice = ref<number>(0);
const sectionRef = ref();
const manageIndex = ref(0);
const {
  drawerVisible,
  editingData,
  checkedRowKeys,
  handleAdd,
} = useTableOperate(data, getData);

function handleUpdateCheckedRowKeys(_: DataTableRowKey[], rows: DataTableRowData[]) {
  let price = 0;
  rows.forEach(item => {
    if (price < item.price_original) {
      price = parseFloat(item.price_original);
    }
  });

  maxPrice.value = price;
}

function showSection(row: Api.Org.Course, index: number) {
  manageIndex.value = index;
  nextTick(() => {
    sectionRef.value.open();
  });
}

function handleUpdatePrice(price: number) {
  handleBatchUpdate({ ids: checkedRowKeys.value, price_sell: price });
}

function handleUpdateStatus(status: number) {
  handleBatchUpdate({ ids: checkedRowKeys.value, status });
}

async function handleBatchUpdate(data: object) {
  const { error } = await batchUpdateCourse(data);

  if (error) {
    return;
  }

  window.$message?.success('操作成功');

  checkedRowKeys.value = [];

  await getData();
}
</script>

<template>
  <div class="flex-col-stretch gap-16px lt-sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="课程列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          :disabled="checkedRowKeys.length === 0"
          @update-status="handleUpdateStatus"
          @update-price="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
        @update:checked-row-keys="handleUpdateCheckedRowKeys"
      />
      <TableOperate
        v-model:visible="drawerVisible"
        :max-price="maxPrice"
        :row-data="editingData"
        @submitted="handleUpdatePrice"
      />
      <TableSection ref="sectionRef" :course="data[manageIndex]" @reset="getData" />
    </NCard>
  </div>
</template>

<style scoped></style>
