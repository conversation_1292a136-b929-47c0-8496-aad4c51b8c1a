<script setup lang="tsx">
import { ref } from 'vue';
import { DataTableRowData, DataTableRowKey, NTag } from 'naive-ui';
import { fetchCoursePackList, batchUpdateCoursePack } from '@/service/api';
import { enableStatusRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import DateFormat from '@/components/common/date/date-format.vue';
import TableHeaderOperation from './modules/table-header-operation.vue';
import TableOperate from './modules/table-operate.vue';
import TableSearch from './modules/table-search.vue';

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchCoursePackList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    status: null,
    keyword: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'id',
      title: 'ID',
      align: 'center'
    },
    {
      key: 'course_pack_id',
      title: '课程包ID',
      align: 'center'
    },
    {
      key: 'content_course_pack',
      title: '课程包名称',
      align: 'center',
      render: (row) => {
        return row.content_course_pack?.content?.title ?? '-';
      }
    },
    {
      key: 'price_original',
      title: '原价',
      align: 'center'
    },
    {
      key: 'price_sell',
      title: '售价',
      align: 'center'
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'error'
        };

        const label = enableStatusRecord[row.status];

        return (
          <NTag type={tagMap[row.status]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    }
  ]
});

const maxPrice = ref<number>(0);

const {
  drawerVisible,
  editingData,
  checkedRowKeys,
  handleAdd,
} = useTableOperate(data, getData);

function handleUpdateCheckedRowKeys(_: DataTableRowKey[], rows: DataTableRowData[]) {
  let price = 0;
  rows.forEach(item => {
    if (price < item.price_original) {
      price = parseFloat(item.price_original);
    }
  });

  maxPrice.value = price;
}


function handleUpdatePrice(price: number) {
  handleBatchUpdate({ ids: checkedRowKeys.value, price_sell: price });
}

function handleUpdateStatus(status: number) {
  handleBatchUpdate({ ids: checkedRowKeys.value, status });
}

async function handleBatchUpdate(data: object) {
  const { error } = await batchUpdateCoursePack(data);

  if (error) {
    return;
  }

  window.$message?.success('操作成功');

  checkedRowKeys.value = [];

  await getData();
}
</script>

<template>
  <div class="flex-col-stretch gap-16px lt-sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="课程列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          :disabled="checkedRowKeys.length === 0"
          @update-status="handleUpdateStatus"
          @update-price="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
        @update:checked-row-keys="handleUpdateCheckedRowKeys"
      />
      <TableOperate
        v-model:visible="drawerVisible"
        :max-price="maxPrice"
        :row-data="editingData"
        @submitted="handleUpdatePrice"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
