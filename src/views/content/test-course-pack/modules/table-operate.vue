<script setup lang="ts">
import { ref, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { showWaningMessage } from "@/utils/message";

interface Props {
  maxPrice: number;
  rowData?: Api.Org.CoursePack | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted', price: number): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = {
  price?: number;
};

const model = ref<Model>({
  price: undefined,
});

const rules: Record<string, App.Global.FormRule> = {
  price: defaultRequiredRule,
};

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  let price = model.value.price as number;

  if (price < props.maxPrice) {
    showWaningMessage(`售价不能低于${props.maxPrice}元`);
    return;
  }

  closeDrawer();
  emit('submitted', model.value.price as number);
}

watch(visible, () => {
  if (visible.value) {
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="620">
    <NDrawerContent title="调整价格" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80">
        <NFormItem label="售价" path="price" class="w-320px">
          <NSpace vertical>
            <NInputNumber v-model:value="model.price" />
            <FormTips :tips="[`售价不能低于${maxPrice}元`]" />
          </NSpace>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit" >{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
