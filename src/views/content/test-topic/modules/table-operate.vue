<script setup lang="ts">
import { ref, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { showWaningMessage } from "@/utils/message";

interface Props {
  maxPrice: number;
  maxPrice_60: number;
  rowData?: Api.Org.Topic | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: "submitted", price: number, price60: number): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = {
  price?: number;
  price_60?: number;
};

const model = ref<Model>({
  price: undefined,
  price_60: undefined,
});

const rules: Record<string, App.Global.FormRule> = {
  price: defaultRequiredRule,
};

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const price = model.value.price as number;
  const price_60 = model.value.price_60 as number;

  if (price < props.maxPrice) {
    showWaningMessage(`30天售价不能低于${props.maxPrice}元`);
    return;
  }

  if (price_60 < props.maxPrice_60) {
    showWaningMessage(`60天售价不能低于${props.maxPrice_60}元`);
    return;
  }

  closeDrawer();
  emit('submitted', model.value.price as number, model.value.price_60 as number);
}

watch(visible, () => {
  if (visible.value) {
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="620">
    <NDrawerContent title="调整价格" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem label="30天售价" path="price" class="w-320px">
          <NSpace vertical>
            <NInputNumber v-model:value="model.price" />
            <FormTips :tips="[`售价不能低于${maxPrice}元`]" />
          </NSpace>
        </NFormItem>
        <NFormItem label="60天售价" path="price" class="w-320px">
          <NSpace vertical>
            <NInputNumber v-model:value="model.price_60" />
            <FormTips :tips="[`售价不能低于${maxPrice_60}元`]" />
          </NSpace>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit" >{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
