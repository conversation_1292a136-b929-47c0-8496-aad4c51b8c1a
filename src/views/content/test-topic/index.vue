<script setup lang="tsx">
import { ref } from 'vue';
import { DataTableRowData, DataTableRowKey, NTag, NButton } from 'naive-ui';
import { fetchTopicList, batchUpdateTopic } from '@/service/api';
import { enableStatusRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import DateFormat from '@/components/common/date/date-format.vue';
import TableHeaderOperation from './modules/table-header-operation.vue';
import TableOperate from './modules/table-operate.vue';
import TableSearch from './modules/table-search.vue';
import TableExamSetting from './modules/table-exam-setting.vue';


const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchTopicList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    status: null,
    keyword: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'id',
      title: 'ID',
      align: 'center'
    },
    {
      key: 'topic_id',
      title: '题库ID',
      align: 'center'
    },
    {
      key: 'topic',
      title: '题库名称',
      align: 'center',
      render: (row) => {
        return row.topic?.name ?? '-';
      }
    },
    {
      key: 'topic.subjects_count' as any,
      title: '题目数量',
      align: 'center',
      render: (row) => {
        return row.topic?.subjects_count;
      }
    },
    {
      key: 'price_original_30',
      title: '30天原价',
      align: 'center',
    },
    {
      key: 'price_sell_30',
      title: '30天售价',
      align: 'center'
    },
    {
      key: 'price_original_60',
      title: '60天原价',
      align: 'center',
    },
    {
      key: 'price_sell_60',
      title: '60天售价',
      align: 'center'
    },
    {
      key: 'exam_time',
      title: '考试时间',
      align: 'center',
      render: row => {
        return `${row.exam_time} 分钟`;
      }
    },
    {
      key: 'pass_score',
      title: '及格分数',
      align: 'center'
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'error'
        };

        const label = enableStatusRecord[row.status];

        return (
          <NTag type={tagMap[row.status]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      render: row => (
        <NButton type="primary" size="small" onClick={() => handleSetting(row)}>
          试卷配置
        </NButton>
      )
    }
  ]
});

const maxPrice = ref<number>(0);
const maxPrice_60 = ref<number>(0);
const examRef = ref();

const {
  drawerVisible,
  editingData,
  checkedRowKeys,
  handleAdd,
} = useTableOperate(data, getData);

function handleUpdateCheckedRowKeys(_: DataTableRowKey[], rows: DataTableRowData[]) {
  let price = 0;
  let price_60 = 0;
  rows.forEach(item => {
    if (price < item.price_original_30) {
      price = parseFloat(item.price_original_30);
    }
    if (price_60 < item.price_original_60) {
      price_60 = parseFloat(item.price_original_60);
    }
  });

  maxPrice.value = price;
  maxPrice_60.value = price_60;
}

function handleUpdatePrice(price: number,price_60: number) {
  handleBatchUpdate({ ids: checkedRowKeys.value, 'price': price, 'price_60': price_60});
}

function handleUpdateStatus(status: number) {
  handleBatchUpdate({ ids: checkedRowKeys.value, status });
}

function handleSetting(row: Api.Org.Topic) {
  examRef.value.open(row);
}

async function handleBatchUpdate(data: object) {
  const { error } = await batchUpdateTopic(data);

  if (error) {
    return;
  }

  window.$message?.success('操作成功');

  checkedRowKeys.value = [];

  await getData();
}
</script>

<template>
  <div class="flex-col-stretch gap-16px  lt-sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="题库列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          :disabled="checkedRowKeys.length === 0"
          @update-status="handleUpdateStatus"
          @update-price="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
        @update:checked-row-keys="handleUpdateCheckedRowKeys"
      />
      <TableOperate
        v-model:visible="drawerVisible"
        :max-price="maxPrice"
        :max-price_60="maxPrice_60"
        :row-data="editingData"
        @submitted="handleUpdatePrice"
      />
      <TableExamSetting ref="examRef" @submitted="getData" />
    </NCard>
  </div>
</template>

<style scoped></style>
