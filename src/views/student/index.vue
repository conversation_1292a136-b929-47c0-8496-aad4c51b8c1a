<script setup lang="tsx">
import { onMounted, ref, watch } from 'vue';
import { NButton, NSpace, NTooltip, NPopconfirm } from 'naive-ui';
import { fetchStudentList, getEnrollmentOptions, removeStudent } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import DateFormat from '@/components/common/date/date-format.vue';
import TableHeaderOperation from './modules/table-header-operation.vue';
import TableOperate from './modules/table-operate.vue';
import TableSearch from './modules/table-search.vue';
import TableDetail from './modules/table-detail.vue';
import TableExpend from './modules/table-expand.vue';
import TableEnrollDetail from './modules/table-enroll-detail.vue';

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchStudentList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    id: null,
    name: null,
    phone: null,
    enrollment_status: null,
    enrollment_class_id: null,
    enrollment_type: null,
    enrollment_resource_id: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48,
    },
    {
      type: 'expand',
      renderExpand: row => (
        <TableExpend
          detail={row}
          onRefresh={handleSuccess}
          onAssign={(row, type) => handleAssign('enrollment', row, type)}
          onSelectEnroll={handleSelectEnroll}
          onEnrollDetail={handleEnrollDetail}
        />
      )
    },
    {
      key: 'id',
      title: 'ID',
      align: 'center',
    },
    {
      key: 'user_id',
      title: '用户ID',
      align: 'center'
    },
    {
      key: 'name',
      title: '姓名',
      align: 'center'
    },
    {
      key: 'phone',
      title: '手机号码',
      align: 'center'
    },
    {
      key: 'work_unit',
      title: '工作单位',
      align: 'center'
    },
    {
      key: 'id_card_number',
      title: '身份证号码',
      align: 'center'
    },
    {
      key: 'latest_enroll_at',
      title: '最近开课时间',
      align: 'center',
      render: row => <DateFormat date={row.latest_enroll_at} />
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      render: row => <DateFormat date={row.created_at} />
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      render: row => <DateFormat date={row.updated_at} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 260,
      render: row => (
        <NSpace size="small" justify="center">
          <NButton type="primary" size="small" onClick={() => show(row)}>
            详情
          </NButton>
          <NButton type="primary" size="small" onClick={() => edit(row)}>
            编辑
          </NButton>
          <NButton type="primary" size="small" onClick={() => handleAssign('student', row)}>
            新开课
          </NButton>
          <NTooltip trigger="hover" placement="top" disabled={row.enrollments?.length == 0 }>
            {{
              default: () => '有报名的学员不可删除',
              trigger: () => (
                <NPopconfirm onPositiveClick={() => remove(row)}>
                  {{
                    default: () => '确定要删除吗？',
                    trigger: () => (
                      <NButton size={'small'} type={'error'} disabled={row.enrollments?.length > 0 }>
                        删除
                      </NButton>
                    )
                  }}
                </NPopconfirm>
              )
            }}
          </NTooltip>
        </NSpace>
      )
    }
  ]
});

const { drawerVisible, editingData, checkedRowKeys, handleOperate } = useTableOperate(data, getData);

const detailRef = ref();
const enrollDetailRef = ref();
const headerOperationRef = ref();
const expandedRowKeys = ref<number[]>([]);
const checkedEnrollmentIds = ref<number[]>([]);
const enrollmentOption = ref<Api.Org.EnrollmentOption | null>(null);
const {
  onDeleted
} = useTableOperate(data, getData);
async function remove(row: Api.Org.Student) {
  const { data } = await removeStudent(row.id);
  if (data != null && data.success == true) {
    onDeleted();
  }
}

function show(row: Api.Org.Student) {
  detailRef.value.open(row);
}

function edit(row: Api.Org.Student) {
  handleOperate('edit', row);
}

function handleAssign(scene: string, row: Api.Org.Enrollment | Api.Org.Student, type?: CommonType.BatchType) {
  headerOperationRef.value.assign(scene, row, type ? type : 'assign');
}

function handleEnrollDetail(row: Api.Org.Enrollment) {
  enrollDetailRef.value.open(row);
}

function handleExpandAll(expand: boolean) {
  if (expand) {
    data.value.forEach((row) => {
      if (!expandedRowKeys.value.includes(row.id)) {
        expandedRowKeys.value.push(row.id);
      }
    })
  } else {
    checkedEnrollmentIds.value = [];
    expandedRowKeys.value = [];
  }
}

function handleSelectEnroll(rowId: number, checked: boolean) {
  checkedRowKeys.value = [];
  if (checked) {
    if (!checkedEnrollmentIds.value.includes(rowId)) {
      checkedEnrollmentIds.value.push(rowId);
    }
  } else {
    const index = checkedEnrollmentIds.value.findIndex(item => item == rowId);

    if (index != -1) {
      checkedEnrollmentIds.value.splice(index, 1);
    }
  }
}

function handleGetData() {
  expandedRowKeys.value = [];

  getDataByPage();
}

function handleSuccess() {
  checkedRowKeys.value = [];
  checkedEnrollmentIds.value = [];
  expandedRowKeys.value = [];

  getData();
}

function handleRefresh() {
  init();

  getData();
}

async function init() {
  const { error, data } = await getEnrollmentOptions();

  if (error) {
    return;
  }

  enrollmentOption.value = data;
}

watch(checkedRowKeys, () => {
  if (checkedRowKeys.value.length > 0) {
    expandedRowKeys.value = [];
    checkedEnrollmentIds.value = [];
  }
});

onMounted(async () => {
  await init();
});
</script>

<template>
  <div class="flex-col-stretch gap-16px lt-sm:overflow-auto">
    <TableSearch v-model:model="searchParams" :option="enrollmentOption" @reset="resetSearchParams" @search="handleGetData" @reload="getData" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          ref="headerOperationRef"
          v-model:columns="columnChecks"
          v-model:student-ids="checkedRowKeys"
          v-model:enrollment-ids="checkedEnrollmentIds"
          :option="enrollmentOption"
          @refresh="handleRefresh"
          @success="handleSuccess"
          @expand="handleExpandAll"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        v-model:expanded-row-keys="expandedRowKeys"
        :columns="columns"
        :data="data"
        :scroll-x="962"
        :loading="loading"
        :row-key="row => row.id"
        :pagination="mobilePagination"
        remote
        size="small"
        class="sm:h-full"
      />
      <TableOperate v-model:visible="drawerVisible" :row-data="editingData" @submitted="getDataByPage" />
      <TableDetail ref="detailRef" />
      <TableEnrollDetail ref="enrollDetailRef" />
    </NCard>
  </div>
</template>

<style scoped>
:deep(.n-data-table-tr--expanded td ul) {
  background-color: #f3f3f3;
}

:deep(.n-data-table-tr--expanded td) {
  padding: 0;
}

:deep(.n-data-table-tr--expanded td:nth-child(even)) {
  padding: 8px 0;
}
</style>