<script setup lang="tsx">
import { ref } from 'vue';
import { classTypeRecord, enrollmentStatusRecord } from "@/constants/business";

const visible = ref<boolean>(false);
const detail = ref<Api.Org.Enrollment | null>(null);

function open(row: Api.Org.Enrollment) {
  detail.value = row;
  visible.value = true;
}

function close() {
  visible.value = false;
}


defineExpose({
  open
})
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="720">
    <NDrawerContent v-if="detail" title="开课详情" :native-scrollbar="false" closable>
      <n-descriptions
        class="mt-6"
        label-placement="left"
        size="large"
        :column="1"
        :label-style="{ 'font-size': '16px', 'font-weight': 600 }"
      >
        <n-descriptions-item label="学员姓名">{{ detail.student.name }}</n-descriptions-item>
        <n-descriptions-item label="手机号码">{{ detail.student.phone }}</n-descriptions-item>
        <n-descriptions-item label="所属班级">{{ detail?.classroom?.name || '-' }}</n-descriptions-item>
        <n-descriptions-item label="已购科目">{{ detail.resource.name }}</n-descriptions-item>
        <n-descriptions-item label="学习状态">{{ enrollmentStatusRecord[detail.status] }}</n-descriptions-item>
        <n-descriptions-item label="支付渠道">{{ detail.class_id > 0 ? '线下' : '线上' }}</n-descriptions-item>
        <n-descriptions-item label="支付金额">{{ detail.class_id > 0 ? detail.amount : detail?.order_info?.amount }}</n-descriptions-item>
        <n-descriptions-item label="业务类型">{{ classTypeRecord[detail.type] }}</n-descriptions-item>
        <n-descriptions-item label="订单状态">已支付</n-descriptions-item>
        <n-descriptions-item label="创建时间"><DateFormat :date="detail.created_at" /></n-descriptions-item>
        <n-descriptions-item label="支付时间"><DateFormat :date="detail.class_id > 0 ? detail.started_at : detail?.order_info?.payment_at" /></n-descriptions-item>

      </n-descriptions>

      <template #footer>
        <NSpace :size="16">
          <NButton @click="close">取消</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
</style>
