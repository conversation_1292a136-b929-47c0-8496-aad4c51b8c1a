<script setup lang="tsx">
import { computed, ref } from 'vue';
import { NButton, NModal, NSpace, NText, NUpload, useDialog } from 'naive-ui';
import type { UploadFileInfo } from 'naive-ui';
import { $t } from '@/locales';
import { classTypeOptions, enrollmentStatusOptions } from '@/constants/business';
import { importEnrollment, importEnrollmentPhoto, importIdCardPhoto } from '@/service/api';
import { getUserInfo } from '@/store/modules/auth/shared';
import { showWaningMessage } from "@/utils/message";
import { KeyboardArrowDownFilled } from '@vicons/material';

interface Props {
  option?: Api.Org.EnrollmentOption | null;
}

defineProps<Props>();

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
  (e: 'reload'): void;
}

type SearchParams = {
  id?: string;
  name?: string;
  phone?: string;
  enrollment_status?: number;
  enrollment_class_id?: number;
  enrollment_type?: string | null;
  enrollment_resource_id?: number | null;
};

// 定义导入结果的接口
interface ImportError {
  row: number;
  message: string;
}

interface ImportResult {
  success: number;
  failed: number;
  errors?: ImportError[];
}

interface ImportPhotoResult {
  success: number;
  failed: number;
  errors?: string[];
}

// 文件上传配置
interface UploadConfig {
  title: string;
  acceptTypes: string;
  acceptExtensions: string[];
  errorMessage: string;
  instructions: string[];
}

const emit = defineEmits<Emits>();
const dialog = useDialog();
const model = defineModel<SearchParams>('model', { required: true });
const userInfo = getUserInfo();

// 上传相关状态
const showUploadModal = ref(false);
const showPhotoUploadModal = ref(false);
const showIdCardUploadModal = ref(false);
const uploadFileList = ref<UploadFileInfo[]>([]);
const photoUploadFileList = ref<UploadFileInfo[]>([]);
const idCardUploadFileList = ref<UploadFileInfo[]>([]);

// 上传配置
const enrollmentUploadConfig: UploadConfig = {
  title: '导入学员',
  acceptTypes: '.xlsx,.xls',
  acceptExtensions: ['xlsx', 'xls'],
  errorMessage: '只能上传Excel文件（.xlsx, .xls）',
  instructions: ['只能上传Excel文件（.xlsx, .xls）']
};

const photoUploadConfig: UploadConfig = {
  title: '导入学员照片',
  acceptTypes: '.zip',
  acceptExtensions: ['zip'],
  errorMessage: '只能上传压缩文件（.zip）',
  instructions: ['只能上传压缩文件（.zip）', '照片尺寸：413px * 626px', '请上传照片文件夹，照片以对应身份证号命名']
};

const idCardUploadConfig: UploadConfig = {
  title: '导入学员身份证',
  acceptTypes: '.zip',
  acceptExtensions: ['zip'],
  errorMessage: '只能上传压缩文件（.zip）',
  instructions: [
    '只能上传压缩文件（.zip）',
    '请上传图片文件夹，照片以对应身份证号命名',
    '身份证人像面 身份证号-1',
    '身份证国徽面 身份证号-2'
  ]
};

// 计算属性
const hasUploadFile = computed(() => uploadFileList.value.length > 0);
const hasPhotoUploadFile = computed(() => photoUploadFileList.value.length > 0);
const hasIdCardUploadFile = computed(() => idCardUploadFileList.value.length > 0);
const dropdownOptions = [
  {
    label: '导入学员',
    key: 'import_enrollments',
  },
  {
    label: '导入学员照片',
    key: 'import_enrollment_photos'
  },
  {
    label: '导入学员身份证',
    key: 'import_id_cards'
  },
  {
    label: '下载导入模板',
    key: 'download_template'
  },
];

// 事件处理函数
function reset() {
  emit('reset');
}

function search() {
  emit('search');
}

function reload() {
  emit('reload');
}

function changeClassType() {
  model.value.enrollment_resource_id = null;
}

function validateFileExtension(file: UploadFileInfo, allowedExtensions: string[]): boolean {
  const fileExt = file.name.split('.').pop()?.toLowerCase() || '';
  if (!allowedExtensions.includes(fileExt)) {
    showWaningMessage(`只能上传${allowedExtensions.join('、')}格式的文件`);
    return false;
  }
  return true;
}

function beforeUpload(data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) {
  return validateFileExtension(data.file, enrollmentUploadConfig.acceptExtensions);
}

function beforePhotoUpload(data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) {
  return validateFileExtension(data.file, photoUploadConfig.acceptExtensions);
}

// 显示导入结果对话框
function showImportResultDialog<T extends ImportResult | ImportPhotoResult>(
  importData: T,
  itemType: string,
  errorFormatter: (err: any) => string
) {
  if (importData.success === 0 && importData.failed > 0) {
    // 全部导入失败
    if (importData.errors && importData.errors.length > 0) {
      const errorMessages =
        Array.isArray(importData.errors) &&
        importData.errors.length > 0 &&
        typeof importData.errors[0] === 'object' &&
        'row' in importData.errors[0]
          ? (importData.errors as ImportError[]).map(errorFormatter)
          : (importData.errors as string[]);

      dialog.error({
        title: '导入错误详情',
        style: 'width: 600px;',
        content: () => (
          <n-scrollbar class="max-h-[300px]">
            <n-space vertical>
              <NText type="error" class="mb-10px font-bold">
                导入失败：共有 {importData.failed} {itemType}导入失败
              </NText>
              {errorMessages.map((msg: string) => (
                <NText type="error">{msg}</NText>
              ))}
            </n-space>
          </n-scrollbar>
        )
      });
    }
  } else if (importData.failed > 0) {
    // 部分导入成功
    if (importData.errors && importData.errors.length > 0) {
      const errorMessages =
        Array.isArray(importData.errors) &&
        importData.errors.length > 0 &&
        typeof importData.errors[0] === 'object' &&
        'row' in importData.errors[0]
          ? (importData.errors as ImportError[]).map(errorFormatter)
          : (importData.errors as string[]);

      dialog.warning({
        title: '导入警告详情',
        style: 'width: 600px;',
        content: () => (
          <n-scrollbar class="max-h-[300px]">
            <n-space vertical>
              <NText type="error" class="mb-10px font-bold">
                导入部分成功：失败 {importData.failed} {itemType}
              </NText>
              {errorMessages.map((msg: string) => (
                <NText type="error">{msg}</NText>
              ))}
            </n-space>
          </n-scrollbar>
        )
      });
    }
  } else {
    // 全部导入成功
    window.$message?.success(`导入成功`);
  }
}

// 处理文件上传
async function handleUploadFile(file: File) {
  try {
    const { error, data } = await importEnrollment(file);
    if (error) {
      window.$message?.error(`导入失败：${error}`);
    } else {
      const importData = data as ImportResult;
      showImportResultDialog(importData, '条学员数据', (err: ImportError) => `第 ${err.row} 行: ${err.message}`);

      reload(); // 重新加载数据
      showUploadModal.value = false;
      uploadFileList.value = [];
    }
  } catch (err) {
    window.$message?.error(`导入过程中发生错误${err}`);
  }
}

async function handleUploadPhotoFile(file: File) {
  try {
    const { error, data } = await importEnrollmentPhoto(file);
    if (error) {
      window.$message?.error(`导入失败：${error}`);
    } else {
      const importData = data as ImportPhotoResult;
      showImportResultDialog(importData, '张照片', (err: string) => err);

      reload(); // 重新加载数据
      showPhotoUploadModal.value = false;
      photoUploadFileList.value = [];
    }
  } catch (err) {
    window.$message?.error(`导入过程中发生错误：${err}`);
  }
}

async function handleUploadIdCardFile(file: File) {
  try {
    const { error, data } = await importIdCardPhoto(file);
    if (error) {
      window.$message?.error(`导入失败：${error}`);
    } else {
      const importData = data as ImportPhotoResult;
      showImportResultDialog(importData, '张身份证照片', (err: string) => err);
      
      reload();
      showIdCardUploadModal.value = false;
      idCardUploadFileList.value = [];
    }
  } catch (err) {
    window.$message?.error(`导入过程中发生错误${err}`);
  }
}

// 文件上传处理
function handleUploadFinish({ file }: { file: UploadFileInfo }) {
  if (file.file) {
    handleUploadFile(file.file);
  }
  return file;
}

function handlePhotoUploadFinish({ file }: { file: UploadFileInfo }) {
  if (file.file) {
    handleUploadPhotoFile(file.file);
  }
  return file;
}

function handleIdCardUploadFinish({ file }: { file: UploadFileInfo }) {
  if (file.file) {
    handleUploadIdCardFile(file.file);
  }
  return file;
}

function handleDropdownSelect(key: string) {
  switch (key) {
    case 'import_enrollments':
      showUploadModal.value = true;
      break;
    case 'import_enrollment_photos':
      showPhotoUploadModal.value = true;
      break;
    case 'import_id_cards':
      showIdCardUploadModal.value = true;
      break;
    case 'download_template':
      // 下载模板文件
      const url = `${userInfo.config?.base_url}/static/excel/学员上传模板.xlsx`;
      window.open(url, '_blank');
      break;
  }
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse :default-expanded-names="['search']">
      <NCollapseItem :title="$t('common.search')" name="search">
        <NForm :model="model">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:2" label="ID" class="pr-10px">
              <NInput v-model:value="model.id" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:2" label="姓名" class="pr-10px">
              <NInput v-model:value="model.name" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:2" label="手机号码" class="pr-10px">
              <NInput v-model:value="model.phone" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:2" label="状态" class="pr-10px">
              <NSelect v-model:value="model.enrollment_status" :options="enrollmentStatusOptions" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:4" label="选择班级" class="pr-10px">
              <NSelect
                v-model:value="model.enrollment_class_id"
                :options="option?.classes"
                label-field="name"
                value-field="id"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:2" label="科目类型" class="pr-10px">
              <NSelect v-model:value="model.enrollment_type" :options="classTypeOptions" @update:value="changeClassType" clearable />
            </NFormItemGi>
            <NFormItemGi v-if="model.enrollment_type === 'course'" span="24 s:12 m:4" class="pr-10px">
              <NSelect
                v-model:value="model.enrollment_resource_id"
                :options="option?.courses"
                label-field="name"
                value-field="course_id"
                placeholder="请选择课程"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi v-if="model.enrollment_type == 'course_pack'" span="24 s:12 m:4" class="pr-10px">
              <NSelect
                v-model:value="model.enrollment_resource_id"
                :options="option?.course_packs"
                label-field="name"
                value-field="course_pack_id"
                placeholder="请选择课程包"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi v-if="model.enrollment_type === 'topic'" span="24 s:12 m:4" class="pr-10px">
              <NSelect
                v-model:value="model.enrollment_resource_id"
                :options="option?.topics"
                label-field="name"
                value-field="topic_id"
                placeholder="请选择题库"
                clearable
              />
            </NFormItemGi>

            <NFormItemGi span="24 s:12 m:3" class="pr-10px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>

            <NFormItemGi span="24 s:12 m:2">
              <n-dropdown trigger="click" :options="dropdownOptions" @select="handleDropdownSelect">
                <n-button type="primary" icon-placement="right">
                  <template #icon>
                    <n-icon>
                      <KeyboardArrowDownFilled />
                    </n-icon>
                  </template>
                  导入学员
                </n-button>
              </n-dropdown>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>

  <!-- 导入学员模态框 -->
  <NModal v-model:show="showUploadModal" preset="dialog" :title="enrollmentUploadConfig.title">
    <NUpload
      v-model:file-list="uploadFileList"
      :max="1"
      :multiple="false"
      :show-file-list="true"
      :accept="enrollmentUploadConfig.acceptTypes"
      :custom-request="() => {}"
      @finish="handleUploadFinish"
      @before-upload="beforeUpload"
    >
      <NButton type="primary">选择Excel文件</NButton>
      <div class="mt-2">
        <p
          v-for="(instruction, index) in enrollmentUploadConfig.instructions"
          :key="index"
          class="mt-1 text-sm text-gray-500"
        >
          {{ instruction }}
        </p>
      </div>
    </NUpload>
    <template #action>
      <NButton @click="showUploadModal = false">取消</NButton>
      <NButton
        type="primary"
        :disabled="!hasUploadFile"
        @click="uploadFileList[0].file && handleUploadFile(uploadFileList[0].file)"
      >
        上传
      </NButton>
    </template>
  </NModal>

  <!-- 导入学员照片模态框 -->
  <NModal v-model:show="showPhotoUploadModal" preset="dialog" :title="photoUploadConfig.title">
    <NUpload
      v-model:file-list="photoUploadFileList"
      :max="1"
      :multiple="false"
      :show-file-list="true"
      :accept="photoUploadConfig.acceptTypes"
      :custom-request="() => {}"
      @finish="handlePhotoUploadFinish"
      @before-upload="beforePhotoUpload"
    >
      <NButton type="primary">选择压缩文件</NButton>
      <div class="mt-2">
        <p
          v-for="(instruction, index) in photoUploadConfig.instructions"
          :key="index"
          class="mt-1 text-sm text-gray-500"
        >
          {{ instruction }}
        </p>
      </div>
    </NUpload>
    <template #action>
      <NButton @click="showPhotoUploadModal = false">取消</NButton>
      <NButton
        type="primary"
        :disabled="!hasPhotoUploadFile"
        @click="photoUploadFileList[0].file && handleUploadPhotoFile(photoUploadFileList[0].file)"
      >
        上传
      </NButton>
    </template>
  </NModal>

    <!-- 导入学员身份证模态框 -->
    <NModal v-model:show="showIdCardUploadModal" preset="dialog" :title="idCardUploadConfig.title">
      <NUpload
        v-model:file-list="idCardUploadFileList"
        :max="1"
        :multiple="false"
        :show-file-list="true"
        :accept="idCardUploadConfig.acceptTypes"
        :custom-request="() => {}"
        @finish="handleIdCardUploadFinish"
        @before-upload="beforePhotoUpload"
      >
        <NButton type="primary">选择压缩文件</NButton>
        <div class="mt-2">
          <p
            v-for="(instruction, index) in idCardUploadConfig.instructions"
            :key="index"
            class="mt-1 text-sm text-gray-500"
          >
            {{ instruction }}
          </p>
        </div>
      </NUpload>
      <template #action>
        <NButton @click="showIdCardUploadModal = false">取消</NButton>
        <NButton
          type="primary"
          :disabled="!hasIdCardUploadFile"
          @click="idCardUploadFileList[0].file && handleUploadIdCardFile(idCardUploadFileList[0].file)"
        >
          上传
        </NButton>
      </template>
  </NModal>
</template>

<style scoped></style>
