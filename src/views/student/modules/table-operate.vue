<script setup lang="tsx">
import { ref, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { updateStudent, getIdCardInfoEnrollment } from '@/service/api';
import { $t } from '@/locales';
import UploadImage from "@/components/common/upload/upload-image.vue";

interface Props {
  rowData?: Api.Org.Student | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = Pick<
  Api.Org.Student,
  'name' | 'phone' | 'id_card_number' | 'id_card_front' | 'id_card_back' | 'photo' | 'work_unit'
>;

const loading = ref<boolean>(false);
const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    phone: '',
    id_card_number: '',
    id_card_front: '',
    id_card_back: '',
    photo: '',
  };
}

const rules: Record<string, App.Global.FormRule> = {
  name: defaultRequiredRule,
  phone: defaultRequiredRule,
  id_card_number: defaultRequiredRule,
  id_card_front: defaultRequiredRule,
  id_card_back: defaultRequiredRule,
  photo: defaultRequiredRule,
};

function closeDrawer() {
  visible.value = false;
}

function handleInitModel() {
  if (props.rowData) {
    Object.assign(model.value, {
      name: props.rowData.name,
      phone: props.rowData.phone,
      id_card_number: props.rowData.id_card_number,
      id_card_front: props.rowData.id_card_front,
      id_card_back: props.rowData.id_card_back,
      photo: props.rowData.id_card_back,
      work_unit: props.rowData.work_unit,
    });
  }
}

async function handleIdCardInfo(value: string) {
  if (value) {
    const { error, data } = await getIdCardInfoEnrollment({ id_card_front: value });

    if (error) {
      return;
    }

    Object.assign(model.value, {
      name: data.card_info.name,
      id_card_number: data.card_info.id_card_number
    })
  }
}

async function handleSubmit() {
  await validate();

  if (props.rowData) {
    loading.value = true;

    const { error } = await updateStudent(props.rowData.id, model.value);

    loading.value = false;

    if (error) {
      return;
    }
  }

  emit('submitted');

  closeDrawer();
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="720">
    <NDrawerContent title="编辑学员信息" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="120">
        <NFormItem label="姓名" path="name" class="w-300px">
          <NInput v-model:value="model.name" />
        </NFormItem>
        <NFormItem label="手机号码" path="phone" class="w-300px">
          <NInput v-model:value="model.phone" />
        </NFormItem>
        <NFormItem label="工作单位" path="work_unit" class="w-420px">
          <NInput v-model:value="model.work_unit" />
        </NFormItem>
        <NFormItem label="身份证号码" path="id_card_number" class="w-420px">
          <NInput v-model:value="model.id_card_number" :disabled="true" />
        </NFormItem>
        <NFormItem label="身份证正面照" path="id_card_front" class="w-460px">
          <UploadImage
            v-model:value="model.id_card_front"
            :preview="rowData?.id_card_front_url"
            :max="1"
            storage="priv"
            @update:value="handleIdCardInfo"
          />
        </NFormItem>
        <NFormItem label="身份证反面照" path="id_card_back" class="w-460px">
          <UploadImage v-model:value="model.id_card_back" :preview="rowData?.id_card_back_url" :max="1" storage="priv" />
        </NFormItem>
        <NFormItem label="照片" path="photo" class="w-460px">
          <UploadImage v-model:value="model.photo" :preview="rowData?.photo_url" :max="1" storage="priv" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit" :disabled="loading">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
