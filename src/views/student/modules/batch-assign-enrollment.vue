<script setup lang="tsx">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { batchAssignEnrollmentSubject, batchRemoveEnrollmentSubject } from '@/service/api';
import { $t } from '@/locales';
import { classTypeOptions } from "@/constants/business";

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate } = useNaiveForm();

type Model = {
  class_id: number | null;
  subject_type: Api.Org.ClassType;
  subject_id: number | null;
};

const model = ref(createDefaultModel());

const visible = ref<boolean>(false);
const loading = ref<boolean>(false);
const ids = ref<number[]>([]);
const classOptions = ref<Api.Org.Class[]>([]);
const courseOptions = ref<Api.Org.OptionCourse[]>([]);
const coursePackOptions = ref<Api.Org.OptionCoursePack[]>([]);
const topicOptions = ref<Api.Org.OptionTopic[]>([]);
const batchType = ref<CommonType.BatchType>('changeClass');

function createDefaultModel(): Model {
  return {
    class_id: null,
    subject_type: 'course',
    subject_id: null,
  };
}


function closeDrawer() {
  visible.value = false;
}

function changeType() {
  model.value.subject_id = null;
}

function changeClass(_: any, option: any) {
  if (option && option.resource_id > 0) {
    model.value.subject_id = option.resource_id;
    model.value.subject_type = option.type;
  }
}

async function handleSubmit() {
  await validate();

  loading.value = true;

  const { error } = await batchAssignEnrollmentSubject({
    type: batchType.value,
    class_id: model.value.class_id,
    enrollment_ids: ids.value,
    subject_type: model.value.subject_type,
    subject_id: model.value.subject_id,
  });

  loading.value = false;

  if (error) {
    return;
  }

  emit('submitted');

  closeDrawer();
}

async function handleRemove(type: string) {
  loading.value = true;

  const { error } = await batchRemoveEnrollmentSubject({
    enrollment_ids: ids.value,
    type
  });

  loading.value = false;

  if (error) {
    return;
  }

  emit('submitted');

  closeDrawer();
}

function open(type: CommonType.BatchType, selectKeys: number[], option: Api.Org.EnrollmentOption, row?: Api.Org.Enrollment) {
  model.value = createDefaultModel();
  batchType.value = type;
  ids.value = selectKeys;
  classOptions.value = option.classes_padding;
  courseOptions.value = option.courses;
  coursePackOptions.value = option.course_packs;
  topicOptions.value = option.topics;

  if (row) {
    if (row.class_id) {
      model.value.class_id = row.class_id;
    }
    if (row.resource_id) {
      model.value.subject_id = row.resource_id;
      model.value.subject_type = row.type;
    }
  }

  visible.value = true;
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="560">
    <NDrawerContent :title="batchType == 'changeClass' ? '更换班级' : (batchType == 'changeSubject' ? '更换科目' : '批量开课') " :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model">
        <NFormItem label="选择班级" path="class_id">
          <NSpace vertical>
            <NSelect
              v-model:value="model.class_id"
              :options="classOptions"
              :disabled="batchType == 'changeSubject'"
              clearable
              label-field="name"
              value-field="id"
              class="w-500px"
              @update:value="changeClass"
            />
          </NSpace>
        </NFormItem>
        <NFormItem label="选择科目" path="subject_id">
          <NFlex vertical>
            <div class="w-500px">
              <n-radio-group v-model:value="model.subject_type" @update:value="changeType" name="type">
                <n-space>
                  <n-radio v-for="item in classTypeOptions" :key="item.value" :value="item.value" :disabled="batchType == 'changeClass'">
                    {{ item.label }}
                  </n-radio>
                </n-space>
              </n-radio-group>
            </div>

            <div class="w-500px">
              <NSelect
                v-if="model.subject_type == 'course'"
                v-model:value="model.subject_id"
                :options="courseOptions"
                :disabled="batchType == 'changeClass'"
                label-field="name"
                value-field="course_id"
                placeholder="请选择课程"
                clearable
              />
              <NSelect
                v-else-if="model.subject_type == 'course_pack'"
                v-model:value="model.subject_id"
                :options="coursePackOptions"
                :disabled="batchType == 'changeClass'"
                label-field="name"
                value-field="course_pack_id"
                placeholder="请选择课程包"
                clearable
              />
              <NSelect
                v-else-if="model.subject_type == 'topic'"
                v-model:value="model.subject_id"
                :options="topicOptions"
                :disabled="batchType == 'changeClass'"
                label-field="name"
                value-field="topic_id"
                placeholder="请选择题库"
                clearable
              />
            </div>
            <FormTips
              :tips="[
                '说明：', '1、学员学业不能是已完成或已过期', '2、7天内可以更换', '3、先退还原科目费用，再补新科目费用'
              ]"
            />
          </NFlex>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit" :disabled="loading">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
