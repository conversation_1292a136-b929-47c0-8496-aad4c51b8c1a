<script setup lang="tsx">
import { ref } from 'vue';
import BatchAssignEnrollment from './batch-assign-enrollment.vue';
import BatchAssignStudent from './batch-assign-student.vue';

interface Props {
  option: Api.Org.EnrollmentOption | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'refresh'): void;
  (e: 'success'): void;
  (e: 'expand', expand: boolean): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<NaiveUI.TableColumnCheck[]>('columns', {
  default: () => []
});
const enrollmentIds = defineModel<number[]>('enrollmentIds', {
  default: () => []
});
const studentIds = defineModel<number[]>('studentIds', {
  default: () => []
});

const enrollmentRef = ref();
const studentRef = ref();
const expandAll = ref<boolean>(false);

function success() {
  emit('success');
}

function handleBatch(type: CommonType.BatchType) {
  const scene = enrollmentIds.value.length > 0 ? 'enrollment' : 'student';

  switch (scene) {
    case 'enrollment':
      enrollmentRef.value.open(type, enrollmentIds.value, props.option);
      break;
    case 'student':
      studentRef.value.open(type, studentIds.value, props.option);
      break;
  }
}

function assign(scene: string, row: Api.Org.Enrollment | Api.Org.Student, type: CommonType.BatchType) {
  switch (scene) {
    case 'enrollment':
      enrollmentRef.value.open(type, [row.id], props.option, row);
      break;
    case 'student':
      studentRef.value.open(type, [row.id], props.option);
      break;
  }
}

function handleExpand(bool: boolean) {
  expandAll.value = bool;

  emit('expand', expandAll.value);
}

defineExpose({
  assign
})
</script>

<template>
  <div>
    <NSpace justify="space-between">
      <NSpace>
        <NText class="pr-8px">学员列表</NText>
        <NButton v-if="!expandAll" size="small" type="default" @click="handleExpand(true)">
          全部展开
        </NButton>
        <NButton v-else size="small" type="default" @click="handleExpand(false)">
          全部收起
        </NButton>
      </NSpace>

      <NSpace>
        <NButton
          size="small"
          type="primary"
          :disabled="!(enrollmentIds.length > 0 || studentIds.length > 0)"
          @click="handleBatch('assign')"
        >
          批量开课
        </NButton>
        <TableColumnSetting v-model:columns="columns" />
      </NSpace>
    </NSpace>

    <BatchAssignEnrollment ref="enrollmentRef" @submitted="success" />
    <BatchAssignStudent ref="studentRef" @submitted="success" />
  </div>
</template>

<style scoped></style>
