<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { NButton, NGrid, NGridItem, NPopconfirm, NSpace, NTag } from "naive-ui";
import { classTypeRecord, enrollmentStatusRecord } from "@/constants/business";
import { batchRemoveEnrollmentSubject, removeEnrollment } from "@/service/api";
import DateFormat from "@/components/common/date/date-format.vue";

interface Props {
  detail: Api.Org.Student;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'refresh'): void;
  (e: 'assign', row: Api.Org.Enrollment, type: CommonType.BatchType): void;
  (e: 'selectEnroll', rowId: number, checked: boolean): void;
  (e: 'enrollDetail', row: Api.Org.Enrollment): void;
}

const emit = defineEmits<Emits>();

const enrollments = ref<Api.Org.Enrollment[]>([]);
const loading = ref<boolean>(false);

function toClassLabel(row: Api.Org.Enrollment) {
  if (row?.classroom) {
    return row.classroom.name;
  }
  return '-';
}

function toTypeLabel(row: Api.Org.Enrollment) {
  if (row?.resource) {
    return `（${classTypeRecord[row.type]}）${row.resource.name}`;
  }
  return '-';
}

function toStatusLabel(row: Api.Org.Enrollment) {
  const tagMap: Record<number, NaiveUI.ThemeColor> = {
    0: 'default',
    1: 'info',
    2: 'success',
    3: 'warning',
    4: 'error',
  }

  return tagMap[row.status];
}

async function handleRemove(row: Api.Org.Enrollment) {
  await removeEnrollment(row.id);

  emit('refresh');
}

function handleAssign(row: Api.Org.Enrollment, type: CommonType.BatchType) {
  emit('assign', row, type);
}

function handleEnrollDetail(row: Api.Org.Enrollment) {
  emit('enrollDetail', row);
}

async function handleRemoveSubject(rowId: number, type: string) {
  loading.value = true;

  const { error } = await batchRemoveEnrollmentSubject({
    enrollment_ids: [rowId],
    type
  });

  loading.value = false;

  if (error) {
    return;
  }

  emit('refresh');
}

function handleCheck(checked: boolean, index: number) {
  enrollments.value[index].checked = checked;

  emit('selectEnroll', enrollments.value[index].id, checked);
}

function reset() {
  enrollments.value.forEach((item) => {
    item.checked = false;
  })
}

onMounted(() => {
  if (props.detail?.enrollments) {

     props.detail.enrollments.forEach((item) => {
       enrollments.value.push({
         ...item,
         checked: false
       })
    });
  }
});

defineExpose({
  reset
})
</script>

<template>
  <NList>
    <NListItem v-for="(item, index) in enrollments" :key="index">
      <div class="list-item">
        <div class="list-item-content">
          <NCheckbox :checked="item.checked" @update:checked="(checked) => handleCheck(checked, index)"></NCheckbox>
        </div>
        <div class="list-item-content w-420px">
          <NGrid :y-gap="2">
            <NGridItem :span="24">所属班级：{{ toClassLabel(item) }}</NGridItem>
            <NGridItem :span="24">已购科目：{{ toTypeLabel(item) }}</NGridItem>
            <NGridItem :span="24">学习状态：
              <NTag :type="toStatusLabel(item)" :bordered="false">
                {{ enrollmentStatusRecord[item.status] }}
              </NTag>
            </NGridItem>
          </NGrid>
        </div>
        <div class="list-item-content w-320px">
          <NGrid :y-gap="2">
            <!-- <NGridItem :span="24">订单金额：{{ item.amount }}</NGridItem> -->
            <NGridItem :span="24">创建时间：<DateFormat :date="item.created_at" /></NGridItem>
            <NGridItem :span="24">支付时间：<DateFormat :date="item.class_id > 0 ? item.started_at : item?.order_info?.payment_at" /></NGridItem>
            <NGridItem :span="24">有效期至：<DateFormat :date="item.class_id > 0 ? item.expired_at : item?.order_info?.expired_at" /></NGridItem>
          </NGrid>
        </div>
        <div class="list-item-content">
          <NSpace size="small" justify="center">
            <NButton v-if="item.status === 1 && item.class_id > 0" type="primary" size="small" @click="handleAssign(item, 'changeClass')">更换班级</NButton>
            <NButton v-if="item.status === 1 && item.resource_id > 0 && item.class_id == 0" type="primary" size="small" @click="handleAssign(item, 'changeSubject')">更换科目</NButton>
            <NPopconfirm v-if="[0, 1].includes(item.status) && item.class_id > 0" @positive-click="handleRemoveSubject(item.id, 'class')">
              <template #trigger>
                <NButton type="warning" size="small" :disabled="loading">移出班级</NButton>
              </template>
              确定要移出班级吗？
            </NPopconfirm>
            <NPopconfirm v-if="[0, 1].includes(item.status) && item.resource_id > 0 && item.class_id == 0" @positive-click="handleRemoveSubject(item.id,'subject')">
              <template #trigger>
                <NButton type="warning" size="small" :disabled="loading">移出科目</NButton>
              </template>
              确定要移出科目吗？
            </NPopconfirm>
             <NButton v-if="item.resource" type="primary" size="small" @click="handleEnrollDetail(item)">开课详情</NButton>
            <NPopconfirm v-if="!item.resource_id && !item.class_id" @positive-click="handleRemove(item)">
              <template #trigger>
                <NButton type="error" size="small">删除</NButton>
              </template>
              确定要删除吗？
            </NPopconfirm>
          </NSpace>
        </div>
      </div>
    </NListItem>
  </NList>
</template>

<style scoped>
.list-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.list-item-content:nth-child(1) {
  padding-left: 24px;
}

.list-item-content:nth-child(n+2) {
  padding: 0 32px;
}
</style>
