<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { NButton, NPopconfirm, NSpace, NTag, NGrid, NGridItem } from 'naive-ui';
import {
  fetchEnrollmentList,
  getEnrollmentOptions,
  removeEnrollment
} from '@/service/api';
import { classTypeRecord, enrollmentStatusRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { pageFail } from "@/utils/tab";
import DateFormat from '@/components/common/date/date-format.vue';
import TableHeaderOperation from './modules/table-header-operation.vue';
import TableSearch from './modules/table-search.vue';
import TableDetail from './modules/table-detail.vue';

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchEnrollmentList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    id: null,
    name: null,
    phone: null,
    status: null,
    class_id: null,
    type: null,
    resource_id: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48,
    },
    {
      key: 'id',
      title: 'ID',
      align: 'center',
    },
    {
      key: 'student',
      title: '学员信息',
      align: 'left',
      render: row => (
        <NGrid y-gap={2}>
          <NGridItem span={24}>姓名：{row.student?.name}</NGridItem>
          <NGridItem span={24}>手机号码：{row.student?.phone}</NGridItem>
          <NGridItem span={24}>身份证号码：{row.student?.id_card_number}</NGridItem>
        </NGrid>
      )
    },
    {
      key: 'class_id',
      title: '班级信息',
      align: 'left',
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'default',
          1: 'info',
          2: 'success',
          3: 'error',
          4: 'warning',
        };

        const label = enrollmentStatusRecord[row.status];

        return (
          <NGrid y-gap={2}>
            <NGridItem span={24}>所属班级：{toClassLabel(row)}</NGridItem>
            <NGridItem span={24}>已购科目：{toTypeLabel(row)}</NGridItem>
            <NGridItem span={24}>学习状态：
              <NTag type={tagMap[row.status]} bordered={false}>
                {label}
              </NTag>
            </NGridItem>
          </NGrid>
        );
      }
    },
    {
      key: 'created_at',
      title: '日期信息',
      align: 'left',
      render: row => (
        <NGrid y-gap={2}>
          <NGridItem span={24}>创建时间：<DateFormat date={row.created_at} /></NGridItem>
          <NGridItem span={24}>支付时间：<DateFormat date={row.started_at}  /></NGridItem>
          <NGridItem span={24}>过期时间：<DateFormat date={row.expired_at} /></NGridItem>
        </NGrid>
      )
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      render: row => (
        <NSpace size="small" justify="center">
          <NButton type="primary" size="small" onClick={() => show(row)}>
            详情
          </NButton>
          <NPopconfirm onPositiveClick={() => remove(row)}>
            {{
              default: () => '确定要删除吗？',
              trigger: () => (
                <NButton size={'small'} type={'error'} disabled={[1, 2, 3, 4].includes(row.status)}>
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </NSpace>
      )
    }
  ]
});

const { checkedRowKeys, onDeleted } = useTableOperate(data, getData);

const detailRef = ref();
const enrollmentOption = ref<Api.Org.EnrollmentOption | null>(null);

function toClassLabel(row: Api.Org.Enrollment) {
  if (row?.classroom) {
    return row.classroom.name;
  }
  return '-';
}

function toTypeLabel(row: Api.Org.Enrollment) {
  if (row?.resource) {
    return `（${classTypeRecord[row.type]}）${row.resource.name}`;
  }
  return '-';
}

function show(row: Api.Org.Enrollment) {
  detailRef.value.open(row, {
    class: toClassLabel(row),
    type: toTypeLabel(row)
  });
}

async function remove(row: Api.Org.Enrollment) {
  await removeEnrollment(row.id);

  await onDeleted();
}

function handleSuccess() {
  checkedRowKeys.value = [];

  getData();
}

function handleRefresh() {
  init();
  getData();
}

async function init() {
  const { error, data } = await getEnrollmentOptions();

  if (error) {
    return;
  }

  enrollmentOption.value = data;
}

onMounted(async () => {
  await pageFail();
  await init();
});
</script>

<template>
  <div class="flex-col-stretch gap-16px lt-sm:overflow-auto">
    <TableSearch v-model:model="searchParams" :option="enrollmentOption" @reset="resetSearchParams" @search="getDataByPage" @reload="getData" />
    <NCard title="报名列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          :disabled="checkedRowKeys.length === 0"
          :search-params="searchParams"
          :checked-row-keys="checkedRowKeys"
          :option="enrollmentOption"
          @refresh="handleRefresh"
          @success="handleSuccess"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        :scroll-x="962"
        :loading="loading"
        :row-key="row => row.id"
        :pagination="mobilePagination"
        remote
        size="small"
        class="sm:h-full"
      />
      <TableDetail ref="detailRef" />
    </NCard>
  </div>
</template>

<style scoped></style>
