<script setup lang="tsx">
import { ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { batchAssignEnrollmentSubject, batchRemoveEnrollmentSubject  } from '@/service/api';
import { $t } from '@/locales';

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = {
  class_id: number | null;
};

const model = ref(createDefaultModel());

const visible = ref<boolean>(false);
const loading = ref<boolean>(false);
const ids = ref<number[]>([]);
const classOptions = ref<Api.Org.Class[]>([]);
const batchType = ref<CommonType.BatchType>('changeClass');


function createDefaultModel(): Model {
  return {
    class_id: null
  };
}

const rules: Record<string, App.Global.FormRule> = {
  class_id: defaultRequiredRule,
};

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  loading.value = true;

  const { error } = await batchAssignEnrollmentSubject({
    class_id: model.value.class_id,
    enrollment_ids: ids.value,
    type: batchType.value
  });

  loading.value = false;

  if (error) {
    return;
  }

  emit('submitted');

  closeDrawer();
}

async function handleRemove() {
  loading.value = true;

  const { error } = await batchRemoveEnrollmentSubject({
    enrollment_ids: ids.value,
    type: 'class'
  });

  loading.value = false;

  if (error) {
    return;
  }

  emit('submitted');

  closeDrawer();
}

function open(type: CommonType.BatchType, selectKeys: number[], option: Api.Org.EnrollmentOption) {
  model.value = createDefaultModel();
  batchType.value = type;
  ids.value = selectKeys;
  classOptions.value = option.classes_padding;
  visible.value = true;
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="520">
    <NDrawerContent :title="batchType == 'changeClass' ? '更换班级' : (batchType == 'changeSubject' ? '更换科目' : '批量分班') " :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="120">
        <NFormItem label="选择班级" path="class_id">
          <NSpace vertical>
            <NSelect class="w-200px" v-model:value="model.class_id" :options="classOptions" label-field="name" value-field="id" clearable/>
            <FormTips
              v-if="batchType == 'changeClass'"
              :tips="[
                '说明：', '1、学员科目要与班级科目保持一致', '2、学员学业不能是已完成或已过期'
              ]"
            />
          </NSpace>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NPopconfirm @positive-click="handleRemove" >
            <template #trigger>
              <NButton type="warning" :disabled="loading">移出班级</NButton>
            </template>
            确定要移出班级吗？
          </NPopconfirm>
          <NButton type="primary" @click="handleSubmit" :disabled="loading">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
