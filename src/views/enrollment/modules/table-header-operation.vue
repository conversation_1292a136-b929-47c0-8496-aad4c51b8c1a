<script setup lang="tsx">
import { ref } from 'vue';
import BatchAssignClass from './batch-assign-class.vue';
import BatchAssignSubject from './batch-assign-subject.vue';

interface Props {
  loading?: boolean;
  disabled?: boolean;
  option?: Api.Org.EnrollmentOption | null;
  searchParams?: Record<string, any>;
  checkedRowKeys?: (string | number)[];
}

const props = defineProps<Props>();

interface Emits {
  (e: 'refresh'): void;
  (e: 'success'): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<NaiveUI.TableColumnCheck[]>('columns', {
  default: () => []
});

const classRef = ref();
const subjectRef = ref();

function refresh() {
  emit('refresh');
}

function success() {
  emit('success');
}

function handleBatch(scene: string, type: CommonType.BatchType) {
  switch (scene) {
    case 'class':
      classRef.value.open(type, props.checkedRowKeys, props.option);
      break;
    case 'subject':
      subjectRef.value.open(type, props.checkedRowKeys, props.option);
      break;
  }
}
</script>

<template>
  <NSpace wrap justify="end" class="lt-sm:w-200px">
    <slot name="default">
      <NButton size="small" type="success" :disabled="disabled" @click="handleBatch('class', 'assign')">
        批量分班
      </NButton>
      <NButton size="small" type="success" :disabled="disabled" @click="handleBatch('subject', 'assign')">
        批量开课
      </NButton>
      <NButton size="small" type="warning" :disabled="disabled" @click="handleBatch('class', 'changeClass')">
        更换班级
      </NButton>
      <NButton size="small" type="warning" :disabled="disabled" @click="handleBatch('subject', 'changeSubject')">
        更换科目（7天内）
      </NButton>
      <ExportButton
        name="下载当前列表"
        export-type="org_enrollment"
        :export-extra="{
          search: searchParams,
          exports: columns,
          enrollment_ids: checkedRowKeys
        }"
      />
    </slot>
    <BatchAssignClass ref="classRef" @submitted="success" />
    <BatchAssignSubject ref="subjectRef" @submitted="success" />
    <TableColumnSetting v-model:columns="columns" />
    <slot name="suffix"></slot>
  </NSpace>
</template>

<style scoped></style>
