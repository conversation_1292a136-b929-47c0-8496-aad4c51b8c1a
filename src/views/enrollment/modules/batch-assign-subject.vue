<script setup lang="tsx">
import { ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { batchAssignEnrollmentSubject, batchRemoveEnrollmentSubject  } from '@/service/api';
import { $t } from '@/locales';
import { classTypeOptions } from "@/constants/business";

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = {
  subject_type: Api.Org.ClassType;
  subject_id: number | null;
};

const model = ref(createDefaultModel());

const visible = ref<boolean>(false);
const loading = ref<boolean>(false);
const ids = ref<number[]>([]);
const courseOptions = ref<Api.Org.OptionCourse[]>([]);
const coursePackOptions = ref<Api.Org.OptionCoursePack[]>([]);
const topicOptions = ref<Api.Org.OptionTopic[]>([]);
const batchType = ref<CommonType.BatchType>('changeSubject');

function createDefaultModel(): Model {
  return {
    subject_type: 'course',
    subject_id: null,
  };
}

const rules: Record<string, App.Global.FormRule> = {
  subject_id: defaultRequiredRule,
};

function closeDrawer() {
  visible.value = false;
}

function changeType() {
  model.value.subject_id = null;
}

async function handleSubmit() {
  await validate();

  loading.value = true;

  const { error } = await batchAssignEnrollmentSubject({
    type: batchType.value,
    enrollment_ids: ids.value,
    subject_type: model.value.subject_type,
    subject_id: model.value.subject_id,
  });

  loading.value = false;

  if (error) {
    return;
  }

  emit('submitted');

  closeDrawer();
}

async function handleRemove() {
  loading.value = true;

  const { error } = await batchRemoveEnrollmentSubject({
    enrollment_ids: ids.value,
    type: 'subject'
  });

  loading.value = false;

  if (error) {
    return;
  }

  emit('submitted');

  closeDrawer();
}

function open(type: CommonType.BatchType, selectKeys: number[], option: Api.Org.EnrollmentOption) {
  model.value = createDefaultModel();
  batchType.value = type;
  ids.value = selectKeys;
  courseOptions.value = option.courses;
  coursePackOptions.value = option.course_packs;
  topicOptions.value = option.topics;
  visible.value = true;
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="520">
    <NDrawerContent :title="batchType == 'changeClass' ? '更换班级' : (batchType == 'changeSubject' ? '更换科目' : '批量开课') " :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="120">
        <NFormItem label="选择科目" path="subject_id">
          <NFlex vertical>
            <div class="w-200px">
              <n-radio-group v-model:value="model.subject_type" @update:value="changeType" name="type">
                <n-space>
                  <n-radio v-for="item in classTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </n-radio>
                </n-space>
              </n-radio-group>
            </div>

            <div class="w-200px">
              <NSelect
                v-if="model.subject_type == 'course'"
                v-model:value="model.subject_id"
                :options="courseOptions"
                label-field="name"
                value-field="course_id"
                placeholder="请选择课程"
                clearable
              />
              <NSelect
                v-else-if="model.subject_type == 'course_pack'"
                v-model:value="model.subject_id"
                :options="coursePackOptions"
                label-field="name"
                value-field="course_pack_id"
                placeholder="请选择课程包"
                clearable
              />
              <NSelect
                v-else-if="model.subject_type == 'topic'"
                v-model:value="model.subject_id"
                :options="topicOptions"
                label-field="name"
                value-field="topic_id"
                placeholder="请选择题库"
                clearable
              />
            </div>
            <FormTips
              v-if="batchType == 'changeSubject'"
              :tips="[
                '说明：', '1、7天内可以更换', '2、先退还原科目费用，再补新科目费用', '3、不能多个班级的学员更换科目'
              ]"
            />
          </NFlex>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NPopconfirm @positive-click="handleRemove" >
            <template #trigger>
              <NButton type="warning" :disabled="loading">移出科目</NButton>
            </template>
            确定要移出科目吗？
          </NPopconfirm>
          <NButton type="primary" @click="handleSubmit" :disabled="loading">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
