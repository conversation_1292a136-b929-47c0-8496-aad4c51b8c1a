<script setup lang="tsx">
import { ref } from 'vue';
import { enrollmentStatusRecord } from '@/constants/business';
import { getFileSize, toHour } from "@/utils/common";
import ImageSingle from "@/components/common/image/image-single.vue";
import DateFormat from "@/components/common/date/date-format.vue";

type LabelType = {
  class: string;
  type: string;
};

const visible = ref<boolean>(false);
const detail = ref<Api.Org.Enrollment | null>(null);
const label = ref<LabelType | null>(null);

function open(row: Api.Org.Enrollment, rowLabel: LabelType) {
  detail.value = row;
  label.value = rowLabel;
  visible.value = true;
}

function close() {
  visible.value = false;
}

function toFilename(filename: string) {
  if (filename.length > 24) {
    return filename.slice(0, 10) + '****' + filename.slice(-10);
  }

  return filename;
}

defineExpose({
  open
})
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="720">
    <NDrawerContent v-if="detail && detail?.student" title="学员信息" :native-scrollbar="false" closable>
      <n-space v-if="detail.student?.photo_url" justify="center">
        <ImageSingle :src="detail.student.photo_url" :width="120" />
      </n-space>

      <n-descriptions
        class="mt-6"
        label-placement="left"
        size="large"
        :column="1"
        :label-style="{ 'font-size': '16px', 'font-weight': 600 }"
      >
        <n-descriptions-item label="姓名">
          {{ detail.student.name }}
        </n-descriptions-item>
        <n-descriptions-item label="手机号码">
          {{ detail.student.phone }}
        </n-descriptions-item>
        <n-descriptions-item label="身份证号码">
          {{ detail.student.id_card_number }}
        </n-descriptions-item>
        <n-descriptions-item label="支付时间">
          <DateFormat :date="detail.started_at" />
        </n-descriptions-item>
        <n-descriptions-item label="到期时间">
          <DateFormat :date="detail.expired_at" />
        </n-descriptions-item>
        <n-descriptions-item label="状态">
          {{ enrollmentStatusRecord[detail.status] }}
        </n-descriptions-item>
        <n-descriptions-item label="学时">
          {{ detail.hour }}
        </n-descriptions-item>
        <n-descriptions-item label="是否考试">
          {{ detail.exam_taken ? '是' : '否' }}
        </n-descriptions-item>
        <n-descriptions-item label="最高成绩">
          {{ detail.exam_score > 0 ? detail.exam_score : '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="所属班级" :span="2">
          {{ label?.class }}
        </n-descriptions-item>
        <n-descriptions-item label="已购科目" :span="2">
          {{ label?.type }}
        </n-descriptions-item>
        <n-descriptions-item label="身份证照片" :span="2">
          <n-image-group>
            <n-space>
              <n-image width="100" :src="detail.student?.id_card_front_url"/>
              <n-image width="100" :src="detail.student?.id_card_back_url"/>
            </n-space>
          </n-image-group>
        </n-descriptions-item>
        <n-descriptions-item v-for="(item, index) in detail.student.extra" :key="index" :label="item.name" :span="2">
          <div v-if="['text', 'textarea' , 'radio', 'select', 'date'].includes(item.type)">{{ item.value }}</div>
          <div v-else-if="['checkbox'].includes(item.type)">{{ item.value.join(',') }}</div>
          <div v-else-if="['region'].includes(item.type)">{{ item.display_text }}</div>
          <div v-else-if="['sign'].includes(item.type)">
            <n-image width="100" :src="item.value"/>
          </div>
          <div v-else-if="['pic'].includes(item.type)">
            <n-space>
              <n-image v-for="(vv, ii) in item.value" :key="ii" width="100" :src="vv.path_url"/>
            </n-space>
          </div>
          <div v-else-if="['file'].includes(item.type)">
            <div v-for="(vv, ii) in item.value" :key="ii">
              <NSpace>
                <NText>{{ toFilename(vv.filename) }}</NText>
                <NText>{{ getFileSize(vv.filesize) }}</NText>
                <NButton text tag="a" target="_blank" type="primary" :href="vv.path_url">查看</NButton>
              </NSpace>
            </div>
          </div>
        </n-descriptions-item>
      </n-descriptions>

      <template #footer>
        <NSpace :size="16">
          <NButton @click="close">取消</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
</style>
