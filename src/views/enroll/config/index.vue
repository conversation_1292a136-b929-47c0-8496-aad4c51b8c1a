<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { NButton, NPopconfirm, NInputNumber, NSpace, NCard, NDataTable } from 'naive-ui';
import { fetchEnrollConfigList, updateEnrollConfig, removeEnrollConfig } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import TableHeaderOperation from './modules/table-header-operation.vue';
import TableOperate from './modules/table-operate.vue';
import TableSearch from './modules/table-search.vue';

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchEnrollConfigList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    title: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'id',
      title: 'ID',
      align: 'center',
      width: 80
    },
    {
      key: 'title',
      title: '课程名称',
      align: 'left',
      minWidth: 200
    },
    {
      key: 'amount',
      title: '价格',
      align: 'center',
      width: 120,
      render: (row) => {
        return `¥${row.amount}`;
      }
    },
    {
      key: 'sort',
      title: '排序',
      align: 'center',
      width: 120,
      render: (row) => {
        return (
          <NInputNumber
            value={row.sort || 0}
            size="small"
            min={0}
            style="width: 80px"
            onUpdateValue={(value) => handleUpdateSort(row.id, value)}
          />
        );
      }
    },
    {
      key: 'actions',
      title: $t('common.action'),
      align: 'center',
      width: 130,
      render: (row) => (
        <NSpace size={8} justify="center">
          <NButton size="small" type="primary" onClick={() => handleEdit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton size="small" type="error">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </NSpace>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys
} = useTableOperate(data, getData);

// 更新排序
async function handleUpdateSort(id: number, sort: number) {
  // 验证排序值
  if (sort < 0) {
    window.$message?.error('排序值不能小于0');
    return;
  }

  // 找到当前记录
  const currentItem = data.value.find(item => item.id === id);
  if (!currentItem) {
    window.$message?.error('记录不存在');
    return;
  }

  try {
    // 传递完整的记录信息给后端
    const updateData = {
      title: currentItem.title,
      amount: currentItem.amount,
      sort: sort
    };

    const { error } = await updateEnrollConfig(id, updateData);
    if (!error) {
      window.$message?.success('排序更新成功');
      // 更新本地数据
      const item = data.value.find(item => item.id === id);
      if (item) {
        item.sort = sort;
      }
    }
  } catch (err) {
    console.error('更新排序失败:', err);
    window.$message?.error('更新排序失败');
  }
}

// 删除课程
async function handleDelete(id: number) {
  const { error } = await removeEnrollConfig(id);
  if (!error) {
    window.$message?.success('删除成功');
    await getDataByPage();
  }
}

// 刷新数据
function handleRefresh() {
  getData();
}

// useTable 默认会自动加载数据，不需要在 onMounted 中再次调用
// onMounted(() => {
//   getData();
// });
</script>

<template>
  <div class="flex-col-stretch gap-16px lt-sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="课程列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          @add="handleAdd"
          @refresh="handleRefresh"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        :scroll-x="800"
        :loading="loading"
        :row-key="row => row.id"
        :pagination="mobilePagination"
        remote
        size="small"
        class="sm:h-full"
      />
      <TableOperate
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
