<script setup lang="ts">
import { NSpace, NButton } from 'naive-ui';
import { $t } from '@/locales';
import TableColumnSetting from '@/components/advanced/table-column-setting.vue';

interface Emits {
  (e: 'add'): void;
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<NaiveUI.TableColumnCheck[]>('columns', { required: true });

const loading = defineModel<boolean>('loading', { default: false });

function add() {
  emit('add');
}

function refresh() {
  emit('refresh');
}
</script>

<template>
  <NSpace wrap justify="end" class="lt-sm:w-200px">
    <slot name="default">
      <NButton size="small" type="primary" @click="add">
        <template #icon>
          <icon-ic-round-plus class="text-icon" />
        </template>
        {{ $t('common.add') }}
      </NButton>
    </slot>
    <TableColumnSetting v-model:columns="columns" />
    <NButton size="small" @click="refresh" :loading="loading">
      <template #icon>
        <icon-ic-round-refresh class="text-icon" />
      </template>
      {{ $t('common.refresh') }}
    </NButton>
    <slot name="suffix"></slot>
  </NSpace>
</template>

<style scoped></style>
