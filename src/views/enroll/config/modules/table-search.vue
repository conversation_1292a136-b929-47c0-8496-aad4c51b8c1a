<script setup lang="ts">
import { NCard, NCollapse, NCollapseItem, NForm, NGrid, NFormItemGi, NInput, NSpace, NButton } from 'naive-ui';
import { $t } from '@/locales';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type SearchParams = {
  name?: string;
}

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', { required: true });

async function reset() {
  emit('reset');
}

async function search() {
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse :default-expanded-names="['search']">
      <NCollapseItem :title="$t('common.search')" name="search">
        <NForm :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:4" label="课程名称" class="pr-24px">
              <NInput v-model:value="model.name" placeholder="请输入课程名称" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:4" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
