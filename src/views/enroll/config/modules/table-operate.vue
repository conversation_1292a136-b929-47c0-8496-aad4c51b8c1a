<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { <PERSON><PERSON><PERSON>er, NDrawerContent, NForm, NFormItem, NInput, NInputNumber, NSpace, NButton } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { createEnrollConfig, updateEnrollConfig } from '@/service/api';
import { useLoading } from '@sa/hooks';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Org.EnrollConfig | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const { loading, startLoading, endLoading } = useLoading();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增课程',
    edit: '编辑课程',
  };
  return titles[props.operateType];
});

type Model = {
  title: string;
  amount: string;
  sort: number | null;
};

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    title: '',
    amount: '',
    sort: 0,
  };
}

const rules: Record<string, App.Global.FormRule> = {
  title: defaultRequiredRule,
  amount: defaultRequiredRule,
  sort: defaultRequiredRule,
};

function handleInitModel() {
  if (props.operateType === 'add') {
    model.value = createDefaultModel();
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, {
      title: props.rowData.title || '',
      amount: props.rowData.amount || '',
      sort: props.rowData.sort || 0,
    });
    return;
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  let response;

  if (props.operateType === 'add') {
    response = await createEnrollConfig(model.value);
  } else if (props.operateType === 'edit' && props.rowData) {
    response = await updateEnrollConfig(props.rowData.id, model.value);
  }

  endLoading();

  if (response && !response.error) {
    window.$message?.success('操作成功');
    closeDrawer();
    emit('submitted');
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="520">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem label="课程名称" path="title" class="w-380px">
          <NInput v-model:value="model.title" placeholder="请输入课程名称" />
        </NFormItem>
        <NFormItem label="价格" path="amount" class="w-200px">
          <NInput v-model:value="model.amount" placeholder="请输入价格">
            <template #prefix>¥</template>
          </NInput>
        </NFormItem>
        <NFormItem label="排序" path="sort" class="w-200px">
          <NInputNumber v-model:value="model.sort" placeholder="请输入排序" :min="0" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
