<script setup lang="tsx">
import { nextTick, onMounted, ref } from "vue";
import { useFormRules, useNaiveForm } from "@/hooks/common/form";
import { getEnrollmentForm, updateEnrollmentForm } from "@/service/api";
import { useLoading } from "@sa/hooks";
import { useTabStore } from '@/store/modules/tab';
import { showWaningMessage } from "@/utils/message";
import BaseRight from "./modules/base-right.vue";
import BaseTable from "./modules/base-table.vue";
import FormFields from "./modules/form-fields.vue";

const { loading, startLoading, endLoading } = useLoading();
const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const tabStore = useTabStore();

type Model = {
  title: string;
  fields: Api.Org.EnrollmentFormField[];
};

const model = ref<Model>({
  title: '',
  fields: []
});

const pageLoading = ref<boolean>(true);
const fieldsRef = ref();
const rightRef = ref();
const tableRef = ref();
const tableRowIndex = ref<number>(-1);

const rules: Record<string, App.Global.FormRule> = {
  title: defaultRequiredRule,
  fields: defaultRequiredRule,
};

async function closePage() {
  await tabStore.removeTabByRouteName('system_enrollment')
}

async function init() {
  const { error, data } = await getEnrollmentForm();

  if (error) {
    showWaningMessage('参数错误');
    await closePage();
    return;
  }


  if (data) {
    Object.assign(model.value, {
      title: data.title,
      fields: data.fields,
    });
  } else {
    Object.assign(model.value, {
      title: '',
      fields: rightRef.value.init(),
    });
  }

  pageLoading.value = false;

  nextTick(() => {
    tableRef.value.init(model.value.fields);
  });
}

async function handleSubmit() {
  await validate();

  startLoading();

  const { error } = await updateEnrollmentForm(model.value);

  endLoading();

  if (error) {
    return;
  }

  window.$message?.success('操作成功');
}

function handleSelect(field: Api.Org.EnrollmentFormField, index: number) {
  tableRowIndex.value = index;

  fieldsRef.value.open(field);
}

function handleUpdate(fields: Api.Org.EnrollmentFormField[]) {
  model.value.fields = fields;
}

function handleTableReload(field: Api.Org.EnrollmentFormField): void {
  tableRef.value.reload(field, tableRowIndex.value);
}

onMounted(async () => {
  await init();
})
</script>

<template>
  <div>
    <NGrid :x-gap="10">
      <NGridItem :span="15">
        <NCard title="报名信息" :bordered="false" >
          <NForm v-if="!pageLoading" ref="formRef" :model="model" :rules="rules">
            <NFormItem path="title" label="报名名称" class="w-60%">
              <NInput v-model:value="model.title" placeholder="请输入报名名称" />
            </NFormItem>

            <NFormItem path="fields" label="表单信息">
              <BaseTable ref="tableRef" @select="handleSelect" @change="handleUpdate" />
            </NFormItem>

            <NSpace>
              <NButton type="primary" :disabled="loading" @click="handleSubmit">保存</NButton>
              <NButton type="default" @click="closePage">关闭</NButton>
            </NSpace>
          </NForm>
        </NCard>
      </NGridItem>
      <NGridItem :span="5">
        <BaseRight ref="rightRef" @select="handleSelect" />
      </NGridItem>
    </NGrid>
    <FormFields ref="fieldsRef" @submitted="handleTableReload"/>
  </div>
</template>

<style scoped></style>
