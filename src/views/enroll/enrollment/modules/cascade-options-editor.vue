<script setup lang="ts">
import { ref, computed } from 'vue';
import { NButton, NInput, NSpace, NCard, NIcon, NPopconfirm, NDivider } from 'naive-ui';
import { AddOutline, TrashOutline, ChevronDownOutline } from '@vicons/ionicons5';

interface CascadeOption {
  label: string;
  value: string;
  children?: {
    label: string;
    value: string;
    children?: {
      label: string;
      value: string;
    }[];
  }[];
}

interface Props {
  value?: CascadeOption[];
}

interface Emits {
  (e: 'update:value', value: CascadeOption[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  value: () => []
});

const emit = defineEmits<Emits>();

const localValue = computed({
  get: () => props.value || [],
  set: (val) => emit('update:value', val)
});

function addFirstLevel() {
  const newOptions = [...localValue.value];
  newOptions.push({
    label: '',
    value: '',
    children: []
  });
  localValue.value = newOptions;
}

function removeFirstLevel(index: number) {
  const newOptions = [...localValue.value];
  newOptions.splice(index, 1);
  localValue.value = newOptions;
}

function addSecondLevel(firstIndex: number) {
  const newOptions = [...localValue.value];
  if (!newOptions[firstIndex].children) {
    newOptions[firstIndex].children = [];
  }
  newOptions[firstIndex].children!.push({
    label: '',
    value: '',
    children: []
  });
  localValue.value = newOptions;
}

function removeSecondLevel(firstIndex: number, secondIndex: number) {
  const newOptions = [...localValue.value];
  newOptions[firstIndex].children!.splice(secondIndex, 1);
  localValue.value = newOptions;
}

function addThirdLevel(firstIndex: number, secondIndex: number) {
  const newOptions = [...localValue.value];
  if (!newOptions[firstIndex].children![secondIndex].children) {
    newOptions[firstIndex].children![secondIndex].children = [];
  }
  newOptions[firstIndex].children![secondIndex].children!.push({
    label: '',
    value: ''
  });
  localValue.value = newOptions;
}

function removeThirdLevel(firstIndex: number, secondIndex: number, thirdIndex: number) {
  const newOptions = [...localValue.value];
  newOptions[firstIndex].children![secondIndex].children!.splice(thirdIndex, 1);
  localValue.value = newOptions;
}

function updateOption(firstIndex: number, secondIndex?: number, thirdIndex?: number, value?: string) {
  const newOptions = [...localValue.value];

  if (thirdIndex !== undefined && secondIndex !== undefined) {
    // 更新三级选项
    newOptions[firstIndex].children![secondIndex].children![thirdIndex].label = value || '';
    newOptions[firstIndex].children![secondIndex].children![thirdIndex].value = value || '';
  } else if (secondIndex !== undefined) {
    // 更新二级选项
    newOptions[firstIndex].children![secondIndex].label = value || '';
    newOptions[firstIndex].children![secondIndex].value = value || '';
  } else {
    // 更新一级选项
    newOptions[firstIndex].label = value || '';
    newOptions[firstIndex].value = value || '';
  }

  localValue.value = newOptions;
}
</script>

<template>
  <div class="cascade-options-editor">
    <div class="mb-16px">
      <NButton type="primary" size="small" @click="addFirstLevel" dashed>
        <template #icon>
          <NIcon><AddOutline /></NIcon>
        </template>
        添加一级选项
      </NButton>
    </div>

    <div v-for="(firstOption, firstIndex) in localValue" :key="firstIndex" class="mb-12px">
      <div class="option-item level-1">
        <div class="option-header">
          <NIcon class="level-icon"><ChevronDownOutline /></NIcon>
          <span class="level-label">一级</span>
          <NInput
            :value="firstOption.label"
            @update:value="(val) => updateOption(firstIndex, undefined, undefined, val)"
            placeholder="请输入选项名称"
            class="flex-1 mx-8px"
            size="small"
          />
          <NButton
            type="error"
            size="small"
            quaternary
            @click="() => removeFirstLevel(firstIndex)"
            class="delete-btn"
          >
            <template #icon>
              <NIcon><TrashOutline /></NIcon>
            </template>
          </NButton>
        </div>

        <div class="option-children">
          <div class="add-child-btn">
            <NButton size="tiny" @click="() => addSecondLevel(firstIndex)" dashed>
              <template #icon>
                <NIcon><AddOutline /></NIcon>
              </template>
              添加二级选项
            </NButton>
          </div>

          <div v-for="(secondOption, secondIndex) in firstOption.children" :key="secondIndex" class="mb-8px">
            <div class="option-item level-2">
              <div class="option-header">
                <NIcon class="level-icon"><ChevronDownOutline /></NIcon>
                <span class="level-label">二级</span>
                <NInput
                  :value="secondOption.label"
                  @update:value="(val) => updateOption(firstIndex, secondIndex, undefined, val)"
                  placeholder="请输入选项名称"
                  class="flex-1 mx-8px"
                  size="small"
                />
                <NButton
                  type="error"
                  size="small"
                  quaternary
                  @click="() => removeSecondLevel(firstIndex, secondIndex)"
                  class="delete-btn"
                >
                  <template #icon>
                    <NIcon><TrashOutline /></NIcon>
                  </template>
                </NButton>
              </div>

              <div class="option-children">
                <div class="add-child-btn">
                  <NButton size="tiny" @click="() => addThirdLevel(firstIndex, secondIndex)" dashed>
                    <template #icon>
                      <NIcon><AddOutline /></NIcon>
                    </template>
                    添加三级选项
                  </NButton>
                </div>

                <div v-for="(thirdOption, thirdIndex) in secondOption.children" :key="thirdIndex" class="mb-6px">
                  <div class="option-item level-3">
                    <div class="option-header">
                      <span class="level-label">三级</span>
                      <NInput
                        :value="thirdOption.label"
                        @update:value="(val) => updateOption(firstIndex, secondIndex, thirdIndex, val)"
                        placeholder="请输入选项名称"
                        class="flex-1 mx-8px"
                        size="small"
                      />
                      <NButton
                        type="error"
                        size="small"
                        quaternary
                        @click="() => removeThirdLevel(firstIndex, secondIndex, thirdIndex)"
                        class="delete-btn"
                      >
                        <template #icon>
                          <NIcon><TrashOutline /></NIcon>
                        </template>
                      </NButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="localValue.length === 0" class="empty-state">
      <div class="empty-icon">
        <NIcon size="48" color="#d9d9d9"><ChevronDownOutline /></NIcon>
      </div>
      <div class="empty-text">暂无联动选项</div>
      <div class="empty-desc">点击上方按钮开始添加选项</div>
    </div>
  </div>
</template>

<style scoped>
.cascade-options-editor {
  max-height: 800px;
  overflow-y: auto;
  padding: 4px;
}

.option-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #fff;
  transition: all 0.2s ease;
}

.option-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.option-item.level-1 {
  border-color: #3b82f6;
  background: #f8faff;
}

.option-item.level-2 {
  border-color: #10b981;
  background: #f0fdf4;
  margin-left: 20px;
}

.option-item.level-3 {
  border-color: #f59e0b;
  background: #fffbeb;
  margin-left: 40px;
}

.option-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 8px;
}

.level-icon {
  color: #6b7280;
  font-size: 14px;
}

.level-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  min-width: 32px;
}

.option-item.level-1 .level-label {
  color: #3b82f6;
}

.option-item.level-2 .level-label {
  color: #10b981;
}

.option-item.level-3 .level-label {
  color: #f59e0b;
}

.option-children {
  padding: 0 12px 12px 12px;
}

.add-child-btn {
  margin-bottom: 8px;
}

.delete-btn {
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.delete-btn:hover {
  opacity: 1;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
}

.empty-icon {
  margin-bottom: 12px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #6b7280;
}

.empty-desc {
  font-size: 14px;
  color: #9ca3af;
}
</style>
