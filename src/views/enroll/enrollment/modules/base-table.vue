<script setup lang="tsx">
import {nextTick, ref} from 'vue';
import {DataTableColumn, NTag, NSpace, NButton, NIcon, NPopconfirm} from "naive-ui";
import { formFieldRecord } from '@/constants/business';
import Sortable from 'sortablejs';

interface Emits {
  (e: 'select', field: Api.Org.EnrollmentFormField, index: number): void;
  (e: 'change', fields: Api.Org.EnrollmentFormField[]): void;
}

const emit = defineEmits<Emits>();

const columns: DataTableColumn[] = [
  {
    title: '排序',
    key: 'index',
    align: 'center',
    width: 80,
    render: (row) => {
      if (row.id === 0) return null;
      return (
        <NIcon size="20">
          <icon-mdi-drag />
        </NIcon>
      );
    }
  },
  {
    title: '序号',
    key: 'key',
    align: 'center',
    render: (_, index) => {
      return `${index + 1}`
    }
  },
  {
    title: '标题',
    key: 'name',
    align: 'center',
  },
  {
    title: '必填',
    key: 'required',
    align: 'center',
    render: row => {
      if (row.required) {
        return <NTag type="error">是</NTag>;
      } else {
        return <NTag>否</NTag>;
      }
    }
  },
  {
    title: '类型',
    key: 'type',
    align: 'center',
    render: row => {
      return formFieldRecord[row.type as Api.Org.EnrollmentFormFieldType];
    }
  },
  {
    title: '操作',
    key: 'operate',
    align: 'center',
    render: (row, index) => {
      if (row.id === 0) return null;

      return (
        <NSpace justify="center">
          <NButton type="primary" onClick={() => handleEdit(row as Api.Org.EnrollmentFormField, index)}>编辑</NButton>
          <NPopconfirm onPositiveClick={() => handleRemove(index)}>
            {{
              default: () => '确定要删除吗？',
              trigger: () => (
                <NButton type="error">删除</NButton>
              )
            }}
          </NPopconfirm>
        </NSpace>
      );
    }
  }
];

const data = ref<Api.Org.EnrollmentFormField[]>([]);

const tableRef = ref();
const loading = ref(false);

const setSort = () => {
  if (tableRef.value) {
    const el = tableRef.value?.$el.querySelector('.n-data-table-tbody');
    Sortable.create(el, {
      filter: '.no-drag', // 过滤掉带有 no-drag 类的元素，使其不可拖动
      onEnd: (evt: any) => {
        const targetRow = data.value.splice(evt.oldIndex, 1)[0];
        data.value.splice(evt.newIndex, 0, targetRow);
      },
    });
  }
};

function handleEdit(row: Api.Org.EnrollmentFormField, index: number) {
  emit('select', row, index);
}

function handleRemove(index: number) {
  data.value.splice(index, 1);

  if (data.value.length <= 0) {
    loading.value = false;
  }

  emit('change', data.value);
}

function init(fields: Api.Org.EnrollmentFormField[]) {
  loading.value = true;
  data.value = fields;

  nextTick(() => {
    setSort();
  })
}

function reload(row: Api.Org.EnrollmentFormField, index: number) {
  if (index < 0) {
    data.value.push(row);
  } else {
    data.value[index] = row;
  }

  // 加载一次
  if (!loading.value) {
    loading.value = true;

    nextTick(() => {
      setSort();
    })
  }

  emit('change', data.value);
}

defineExpose({
  init,
  reload
});
</script>

<template>
  <NDataTable 
    ref="tableRef" 
    :row-key="row => row.id" 
    :columns="columns" 
    :data="data" 
    :row-props="row => {
      return {
        class: row.id === 0 ? 'no-drag' : ''
      }
    }"
  />
</template>

<style scoped></style>