<script setup lang="ts">
import { ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { formFieldOptions } from '@/constants/business';
import CascadeOptionsEditor from './cascade-options-editor.vue';

interface Emits {
  (e: 'submitted', field: Api.Org.EnrollmentFormField): void;
}

const emit = defineEmits<Emits>();

const visible = ref<boolean>(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const model = ref<Api.Org.EnrollmentFormField>(createDefaultModel());

function createDefaultModel(): Api.Org.EnrollmentFormField {
  return {
    type: 'text',
    name: '',
    required: false,
    config: {
      mask: 'string',
      options: [],
      cascadeOptions: []
    }
  };
}

const rules: Record<string, App.Global.FormRule> = {
  type: defaultRequiredRule,
  name: defaultRequiredRule,
  required: defaultRequiredRule,
};

async function handleSubmit() {
  await validate();

  emit('submitted', model.value);

  closeDrawer();
}

function closeDrawer() {
  visible.value = false;
}

function open(field: Api.Org.EnrollmentFormField) {
  restoreValidation();

  model.value = field;
  visible.value = true;
}

defineExpose({
  open
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="720">
    <NDrawerContent title="设置表单信息" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
        <NFormItem label="字段类型" path="type" class="w-320px">
          <NSelect v-model:value="model.type" :options="formFieldOptions" :disabled="true" />
        </NFormItem>
        <NFormItem label="字段名称" path="name" class="w-320px">
          <NInput v-model:value="model.name" />
        </NFormItem>
        <NFormItem label="是否必填" path="required" class="w-320px">
          <NSwitch v-model:value="model.required" />
        </NFormItem>
        <NFormItem label="占位提示" path="placeholder" class="w-520px">
          <NInput v-model:value="model.placeholder" />
        </NFormItem>
        <NFormItem label="字段描述" path="desc" class="w-520px">
          <NInput type="textarea" v-model:value="model.desc" :rows="3" />
        </NFormItem>
        <NFormItem v-if="model.type === 'text' && model.config?.mask" label="类型">
          <NRadioGroup v-model:value="model.config.mask" clearable>
            <NRadio value="string" label="字符串" />
            <NRadio value="int" label="数字" />
            <NRadio value="tel" label="手机号码" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem v-if="['text', 'textarea'].includes(model.type) && model.config?.min != undefined" :label="model.config?.mask == 'int' ? '最小值' : '最小长度'" class="w-240px">
          <NInputNumber v-model:value="model.config.min" :min="0" />
        </NFormItem>
        <NFormItem v-if="['text', 'textarea'].includes(model.type) && model.config?.max != undefined" :label="model.config?.mask == 'int' ? '最大值' : '最大长度'" class="w-240px">
          <NInputNumber v-model:value="model.config.max" :min="0"  />
        </NFormItem>
        <NFormItem v-if="['select', 'checkbox', 'radio'].includes(model.type) && model.config?.options" label="选项" class="w-520px">
          <NDynamicTags v-model:value="model.config.options" />
        </NFormItem>
        <NFormItem v-if="model.type === 'cascade' && model.config?.cascadeOptions" label="联动选项" class="w-520px">
          <CascadeOptionsEditor v-model:value="model.config.cascadeOptions" />
        </NFormItem>
        <NFormItem v-if="['pic', 'file'].includes(model.type) && model.config?.limit != undefined" label="上传数量" class="w-260px">
          <NInputNumber v-model:value="model.config.limit">
            <template #suffix>{{ model.type == 'pic' ? '张' : '个' }}</template>
          </NInputNumber>
        </NFormItem>
        <NFormItem v-if="['pic', 'file'].includes(model.type) && model.config?.size != undefined" label="单个文件大小" class="w-260px">
          <NInputNumber v-model:value="model.config.size">
            <template #suffix>KB</template>
          </NInputNumber>
        </NFormItem>
        <NFormItem v-if="model.type === 'file' && model.config?.ext" label="文件类型">
          <NCheckboxGroup v-model:value="model.config.ext">
            <NCheckbox value="*" label="不限制"></NCheckbox>
            <NCheckbox value="doc" label="文档"></NCheckbox>
            <NCheckbox value="video" label="视频"></NCheckbox>
            <NCheckbox value="image" label="图片"></NCheckbox>
            <NCheckbox value="archive" label="压缩包"></NCheckbox>
          </NCheckboxGroup>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确认</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
