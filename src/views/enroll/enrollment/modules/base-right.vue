<script setup lang="tsx">
import { uuid } from '@/utils/common';
import { formFieldOptions } from '@/constants/business';

interface Emits {
  (e: 'select', field: Api.Org.EnrollmentFormField, index: number): void;
}

const emit = defineEmits<Emits>();

const baseFields: Api.Org.EnrollmentFormField[] = [
  {
    type: 'text',
    name: '姓名',
    required: true,
    placeholder: '请输入姓名',
    desc: '',
    config: {
      mask: 'string',
      max: 4
    }
  },
  {
    type: 'radio',
    name: '性别',
    required: true,
    placeholder: '',
    desc: '',
    config: {
      options: ['男','女']
    }
  },
  {
    type: 'photo',
    name: '证件照',
    required: false,
    desc: '',
    config: {
      limit: 1
    }
  },
  {
    type: 'text',
    name: '联系方式',
    required: false,
    placeholder: '请输入手机号码',
    desc: '',
    config: {
      mask: 'tel',
      max: 11
    }
  },
  {
    type: 'work_unit',
    name: '工作单位',
    required: false,
    placeholder: '请输入工作单位',
    desc: '',
    config: {
      mask: 'string',
      min: 2,
    }
  }
];

function handleSelect(index: number) {
  emit('select', Object.assign({}, { id: uuid(), ...baseFields[index] }), -1);
}

function handleSelectField(type: Api.Org.EnrollmentFormFieldType) {
  const typeOptions: Record<Api.Org.EnrollmentFormFieldType, Api.Org.EnrollmentFormField> = {
    text: {
      type: 'text',
      name: '',
      required: false,
      placeholder: '请输入',
      config: {
        mask: 'string',
        min: 0,
        max: 0,
      }
    },
    textarea: {
      type: 'textarea',
      name: '',
      placeholder: '请输入',
      required: false,
      config: {
        mask: 'string',
        max: 0
      }
    },
    radio: {
      type: 'radio',
      name: '',
      required: false,
      config: {
        options: []
      }
    },
    checkbox: {
      type: 'checkbox',
      name: '',
      required: false,
      config: {
        options: []
      }
    },
    select: {
      type: 'select',
      name: '',
      placeholder: '请选择',
      required: false,
      config: {
        options: []
      }
    },
    cascade: {
      type: 'cascade',
      name: '',
      placeholder: '请选择',
      required: false,
      config: {
        cascadeOptions: []
      }
    },
    region: {
      type: 'region',
      name: '',
      placeholder: '请选择地区',
      required: false
    },
    date: {
      type: 'date',
      name: '',
      placeholder: '请选择日期',
      required: false
    },
    pic: {
      type: 'pic',
      name: '',
      required: false,
      config: {
        limit: 1,
        size: 1024 * 2
      }
    },
    file: {
      type: 'file',
      name: '',
      required: false,
      config: {
        ext: ['*'], // *,doc,video,image,archive
        limit: 1,
        size: 1024 * 10
      }
    },
    sign: {
      type: 'sign',
      name: '',
      required: false
    },
    photo: {
      type: 'photo',
      name: '',
      required: false
    },
    work_unit: {
      type: 'work_unit',
      name: '',
      required: false,
      placeholder: '请输入',
      config: {
        mask: 'string',
        min: 0,
        max: 0,
      }
    },
  }

  emit('select', Object.assign({}, { id: uuid(), placeholder: '', desc: '', ...typeOptions[type] }), -1);
}

function init(): Api.Org.EnrollmentFormField[] {
  return [];
}

defineExpose({
  init
});
</script>

<template>
  <NCard title="学员信息" :bordered="false">
    <div class="form-menus">基础信息</div>
    <NSpace vertical size="small">
      <NTag v-for="(item, index) in baseFields" :key="index" checkable @click="() => handleSelect(index)">{{ item.name }}</NTag>
    </NSpace>

    <n-divider />

    <div class="form-menus">常用工具</div>
    <NSpace vertical size="small">
      <NTag v-for="item in formFieldOptions" :key="item.value" checkable @click="() => handleSelectField(item.value)">{{ item.label }}</NTag>
    </NSpace>
  </NCard>
</template>

<style scoped>
  .form-menus {
    margin-bottom: 10px;
    font-size: 14px;
  }
</style>
