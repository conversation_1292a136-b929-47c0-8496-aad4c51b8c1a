<script setup lang="ts">
import { ref, watch } from 'vue';
import { NModal, NCard, NForm, NFormItem, NRadioGroup, NRadio, NInput, NSpace, NButton } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { auditEnrollRecord } from '@/service/api';
import { useLoading } from '~/packages/hooks';

interface Props {
  record?: Api.Org.EnrollRecord | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'success'): void;
}

const emit = defineEmits<Emits>();

const { loading, startLoading, endLoading } = useLoading();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const visible = ref(false);

type Model = {
  status: number;
  remark: string;
};

const model = ref<Model>({
  status: 0,
  remark: ''
});

const rules: Record<string, App.Global.FormRule> = {
  status: defaultRequiredRule,
  remark: [
    {
      required: true,
      message: '请输入审核意见',
      trigger: ['blur', 'input']
    }
  ]
};

function open() {
  visible.value = true;
  model.value = {
    status: 0,
    remark: ''
  };
  restoreValidation();
}

function close() {
  visible.value = false;
}

async function handleSubmit() {
  if (!props.record) return;

  await validate();

  startLoading();

  const { error } = await auditEnrollRecord(props.record.id, {
    status: model.value.status,
    remark: model.value.remark
  });

  endLoading();

  if (!error) {
    window.$message?.success('审核成功');
    close();
    emit('success');
  }
}

defineExpose({
  open,
  close
});
</script>

<template>
  <NModal v-model:show="visible" preset="card" :title="record?.status === 0 ? '审核报名' : (record?.status === 4 ? '审核退款' : '审核')" style="width: 520px">
    <div v-if="record">
      <div class="mb-20px p-16px bg-gray-50 rd-8px">
        <div class="text-14px text-gray-500 mb-8px">申请人信息</div>
        <div class="flex items-center mb-8px">
          <div class="text-16px font-medium text-gray-900">{{ record.student?.name || '-' }}</div>
          <div class="mx-8px text-gray-400">-</div>
          <div class="text-16px text-gray-700">{{ record.student?.phone || '-' }}</div>
        </div>
        <div class="text-14px text-gray-500">
          身份证号：<span class="text-gray-700">{{ record.student?.id_card_number || '-' }}</span>
        </div>
        <div v-if="record.status === 4">
          <div class="text-14px text-gray-500">
            报名课程：<span class="text-gray-700">{{ record.enroll_config?.title || '-' }}</span>
          </div>
          <div class="text-14px text-gray-500">
            报名价格：<span class="text-gray-700">{{ record.enroll_config?.amount || '-' }}</span>
            <div v-if="record.order?.payment_amount">
              支付金额：<span class="text-gray-700">¥{{ record.order?.payment_amount }}</span>
            </div>
          </div>
        </div>
      </div>

      <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80">
        <NFormItem label="审核意见" path="status">
          <NRadioGroup v-model:value="model.status">
            <NSpace>
              <NRadio :value="1" class="audit-radio audit-radio-success">
                <span class="flex items-center">
                  <span class="mr-4px">✓</span>
                  通过
                </span>
              </NRadio>
              <NRadio :value="2" class="audit-radio audit-radio-error">
                <span class="flex items-center">
                  <span class="mr-4px">✗</span>
                  驳回
                </span>
              </NRadio>
            </NSpace>
          </NRadioGroup>
        </NFormItem>
        <NFormItem v-if="model.status === 2" label="驳回原因" path="remark">
          <NInput
            v-model:value="model.remark"
            type="textarea"
            placeholder="请输入驳回原因..."
            :rows="4"
            maxlength="200"
            show-count
            class="audit-textarea"
          />
        </NFormItem>
      </NForm>
    </div>

    <template #footer>
      <NSpace justify="end">
        <NButton @click="close" class="cancel-btn">取消</NButton>
        <NButton type="primary" :loading="loading" @click="handleSubmit" class="submit-btn">
          确认审核
        </NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped>
.audit-radio {
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.audit-radio-success:deep(.n-radio--checked) {
  color: #52c41a;
}

.audit-radio-error:deep(.n-radio--checked) {
  color: #f5222d;
}

.audit-textarea:deep(.n-input__textarea) {
  border-radius: 6px;
  transition: border-color 0.2s ease;
}

.audit-textarea:deep(.n-input__textarea:focus) {
  border-color: rgb(var(--primary-color));
  box-shadow: 0 0 0 2px rgba(var(--primary-color), 0.1);
}

.cancel-btn {
  padding: 0 20px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.submit-btn {
  padding: 0 20px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.submit-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--primary-color), 0.3);
}

/* 模态框样式优化 */
:deep(.n-card) {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

:deep(.n-card-header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.n-card-header .n-card-header__main) {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

:deep(.n-card__content) {
  padding: 20px 24px;
}

:deep(.n-card__footer) {
  padding: 16px 24px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}
</style>
