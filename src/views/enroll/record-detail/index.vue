<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { NCard, NGrid, NGridItem, NDescriptions, NDescriptionsItem, NImage, NTag, NSpace, NButton, NTimeline, NTimelineItem, NSpin, NText } from 'naive-ui';
import { getEnrollRecord } from '@/service/api';
import { useLoading } from '~/packages/hooks';
import DateFormat from '@/components/common/date/date-format.vue';
import AuditModal from './modules/audit-modal.vue';

const route = useRoute();
const router = useRouter();
const { loading, startLoading, endLoading } = useLoading();

const record = ref<Api.Org.EnrollRecord | null>(null);
const auditModalRef = ref();

// 文件大小格式化函数
function getFileSize(size: number): string {
  if (size < 1024) return size + 'B';
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB';
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB';
  return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB';
}

// 文件名提取函数
function toFilename(filename: string): string {
  return filename.split('/').pop() || filename;
}

async function fetchData() {
  const id = Number(route.query.id);
  if (!id) {
    window.$message?.error('参数错误');
    router.back();
    return;
  }

  startLoading();
  const { data, error } = await getEnrollRecord(id);
  endLoading();

  if (error) {
    window.$message?.error('获取数据失败');
    this.goBack();
    return;
  }

  record.value = data;
}

function handleAudit() {
  auditModalRef.value?.open();
}

function handleAuditSuccess() {
  fetchData();
}

function goBack() {
  router.push(`/enroll/record`);
}

onMounted(async () => {
  await fetchData();
});
</script>

<template>
  <div v-bind="$attrs">
    <div v-if="record" class="flex gap-20px lt-md:flex-col">
      <!-- 左侧主要内容 -->
      <div class="flex-1">
        <!-- 头部标题和操作 -->
        <NCard title="报名信息" :bordered="false" size="small" class="mb-20px card-wrapper">

          <template #header-extra>
            <NSpace>
              <NButton v-if="record.status === 0 || record.status === 4" type="primary" @click="handleAudit">
                审核
              </NButton>
              <NButton @click="goBack">返回</NButton>
            </NSpace>
          </template>

          <!-- 申请人基本信息 -->
          <div class="mb-24px p-16px bg-gray-50 rd-8px">
            <div class="text-16px font-medium mb-12px text-primary-600">基本信息</div>
            <NGrid :cols="2" :x-gap="24" :y-gap="16">
              <!-- 报名课程 -->
              <NGridItem>
                <div class="flex items-center">
                  <div class="text-14px text-gray-500 w-80px">报名课程：</div>
                  <div class="text-16px font-medium">{{ record.enroll_config?.title || '-' }}</div>
                </div>
              </NGridItem>
              <!-- 报名金额 -->
              <NGridItem>
                <div class="flex items-center">
                  <div class="text-14px text-gray-500 w-80px">报名价格：</div>
                  <div class="text-16px font-medium">{{ record.enroll_config?.amount || '-' }}</div>
                </div>
              </NGridItem>
              <!-- 姓名 -->
              <NGridItem>
                <div class="flex items-center">
                  <div class="text-14px text-gray-500 w-80px">姓名：</div>
                  <div class="text-16px font-medium">{{ record.student?.name || '-' }}</div>
                </div>
              </NGridItem>

              <!-- 身份证号 -->
              <NGridItem>
                <div class="flex items-center">
                  <div class="text-14px text-gray-500 w-80px">身份证号：</div>
                  <div class="text-16px">{{ record.student?.id_card_number || '-' }}</div>
                </div>
              </NGridItem>

              <!-- 手机号 -->
              <NGridItem>
                <div class="flex items-center">
                  <div class="text-14px text-gray-500 w-80px">手机号：</div>
                  <div class="text-16px">{{ record.student?.phone || '-' }}</div>
                </div>
              </NGridItem>

              <!-- 工作单位 -->
              <NGridItem>
                <div class="flex items-center">
                  <div class="text-14px text-gray-500 w-80px">工作单位：</div>
                  <div class="text-16px">{{ record.student?.work_unit || '-' }}</div>
                </div>
              </NGridItem>
            </NGrid>
          </div>

          <!-- 证件照片 -->
          <div class="mb-24px p-16px bg-gray-50 rd-8px">
            <div class="text-16px font-medium mb-16px text-primary-600">证件照片</div>
            <NGrid :cols="3" :x-gap="24" :y-gap="24">
              <!-- 身份证正面 -->
              <NGridItem>
                <div class="flex flex-col items-center">
                  <div class="text-14px font-medium mb-8px">身份证正面</div>
                  <div class="id-card-container">
                    <NImage
                      v-if="record.student?.id_card_front_url"
                      :src="record.student.id_card_front_url"
                      width="220"
                      height="140"
                      object-fit="cover"
                      preview-disabled
                      class="shadow-sm rd-4px"
                    />
                    <div v-else class="w-220px h-140px bg-gray-100 flex items-center justify-center text-gray-400 border border-dashed rd-4px">
                      暂无图片
                    </div>
                  </div>
                </div>
              </NGridItem>

              <!-- 身份证背面 -->
              <NGridItem>
                <div class="flex flex-col items-center">
                  <div class="text-14px font-medium mb-8px">身份证背面</div>
                  <div class="id-card-container">
                    <NImage
                      v-if="record.student?.id_card_back_url"
                      :src="record.student.id_card_back_url"
                      width="220"
                      height="140"
                      object-fit="cover"
                      preview-disabled
                      class="shadow-sm rd-4px"
                    />
                    <div v-else class="w-220px h-140px bg-gray-100 flex items-center justify-center text-gray-400 border border-dashed rd-4px">
                      暂无图片
                    </div>
                  </div>
                </div>
              </NGridItem>

              <!-- 证件照 -->
              <NGridItem>
                <div class="flex flex-col items-center">
                  <div class="text-14px font-medium mb-8px">证件照</div>
                  <div class="id-card-container">
                    <NImage
                      v-if="record.student?.photo_url"
                      :src="record.student.photo_url"
                      width="120"
                      height="160"
                      object-fit="cover"
                      preview-disabled
                      class="shadow-sm rd-4px"
                    />
                    <div v-else class="w-120px h-160px bg-gray-100 flex items-center justify-center text-gray-400 border border-dashed rd-4px">
                      暂无图片
                    </div>
                  </div>
                </div>
              </NGridItem>
            </NGrid>
          </div>

          <!-- 附加信息 -->
          <div class="mb-24px p-16px bg-gray-50 rd-8px">
            <div class="text-16px font-medium mb-16px text-primary-600">其它信息</div>
            <NGrid :cols="2" :x-gap="24" :y-gap="16">
              <!-- 动态字段 -->
              <template v-for="(item, index) in record.student?.extra" :key="index">
                <NGridItem v-if="item.type !== 'work_unit'" :span="item.type === 'textarea' ? 2 : 1">
                  <div class="field-item">
                    <div class="text-14px text-gray-500 mb-4px">{{ item.name }}：</div>
                    <div v-if="['text', 'textarea', 'radio', 'select', 'date'].includes(item.type)" class="text-16px">
                      {{ item.value || '-' }}
                    </div>
                    <div v-else-if="['checkbox'].includes(item.type)" class="text-16px">
                      {{ Array.isArray(item.value) ? item.value.join(',') : item.value || '-' }}
                    </div>
                    <div v-else-if="['region', 'cascade'].includes(item.type)" class="text-16px">
                      {{ item.display_text || '-' }}
                    </div>
                    <div v-else-if="['pic'].includes(item.type)">
                      <NSpace v-if="item.value && item.value.length">
                        <NImage
                          v-for="(vv, ii) in item.value"
                          :key="ii"
                          width="100"
                          height="100"
                          object-fit="cover"
                          :src="vv.path_url"
                          preview-disabled
                          class="shadow-sm rd-4px"
                        />
                      </NSpace>
                      <div v-else class="text-gray-400">-</div>
                    </div>
                    <div v-else-if="['sign'].includes(item.type)">
                      <div v-if="item.value" class="sign-container">
                        <NImage
                          :src="item.value"
                          width="200"
                          height="100"
                          object-fit="contain"
                          preview-disabled
                          class="shadow-sm rd-4px bg-white border"
                        />
                        <div class="text-12px text-gray-500 mt-4px">电子签名</div>
                      </div>
                      <div v-else class="text-gray-400">-</div>
                    </div>
                    <div v-else-if="['file'].includes(item.type)">
                      <div v-if="item.value && item.value.length">
                        <div v-for="(vv, ii) in item.value" :key="ii" class="mb-8px p-8px bg-gray-100 rd-4px">
                          <NSpace align="center">
                            <NText class="text-primary-600">{{ toFilename(vv.filename) }}</NText>
                            <NText class="text-gray-500">{{ getFileSize(vv.filesize) }}</NText>
                            <NButton text tag="a" target="_blank" type="primary" :href="vv.path_url">查看</NButton>
                          </NSpace>
                        </div>
                      </div>
                      <div v-else class="text-gray-400">-</div>
                    </div>
                  </div>
                </NGridItem>
              </template>
            </NGrid>
          </div>
        </NCard>
      </div>

      <!-- 右侧流转记录 -->
      <div class="w-320px lt-md:w-full">
        <NCard title="流转记录" :bordered="false" size="small" class="card-wrapper">
          <div class="p-8px">
            <NTimeline>
              <NTimelineItem
                v-for="(operateRecord, index) in record.operate_records"
                :key="index"
                :title="operateRecord.type_label"
                type="info"
                line-type="dashed"
              >
                <template #default>
                  <div class="text-12px text-gray-500">
                    <div v-html="operateRecord.remark"></div>
                    <DateFormat :date="operateRecord.created_at" />
                  </div>
                </template>
              </NTimelineItem>
            </NTimeline>
          </div>
        </NCard>
      </div>
    </div>

    <div v-else-if="loading" class="flex items-center justify-center h-400px">
      <NSpin size="large" />
    </div>

    <AuditModal
      ref="auditModalRef"
      :record="record"
      @success="handleAuditSuccess"
    />
  </div>
</template>

<style scoped>
.border {
  border: 1px solid #e0e0e6;
}
.border-dashed {
  border-style: dashed;
}

.card-wrapper {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  transition: box-shadow 0.3s ease;
}

.card-wrapper:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.id-card-container {
  position: relative;
  transition: transform 0.2s ease;
}

.id-card-container:hover {
  transform: translateY(-2px);
}

.field-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.field-item:last-child {
  border-bottom: none;
}

.text-primary-600 {
  color: rgb(var(--primary-600-color, 79 70 229));
}

/* 响应式优化 */
@media (max-width: 768px) {
  .id-card-container .n-image,
  .id-card-container > div {
    width: 180px !important;
    height: 110px !important;
  }

  .id-card-container .n-image[style*="120px"] {
    width: 100px !important;
    height: 130px !important;
  }
}

/* 时间线样式优化 */
:deep(.n-timeline-item-content) {
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 8px;
}

:deep(.n-timeline-item-content:hover) {
  background: #e9ecef;
}

/* 图片预览优化 */
:deep(.n-image) {
  border-radius: 4px;
  overflow: hidden;
}

/* 文件链接样式 */
:deep(.n-button[tag="a"]) {
  text-decoration: none;
}

:deep(.n-button[tag="a"]:hover) {
  text-decoration: underline;
}

/* 签名容器样式 */
.sign-container {
  display: inline-block;
  text-align: center;
}

.sign-container .n-image {
  border: 1px solid #e0e0e6;
  background: white;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.sign-container .n-image:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
