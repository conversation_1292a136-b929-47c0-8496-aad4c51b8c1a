<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { NButton, NSpace, NTag, NCard, NDataTable } from 'naive-ui';
import { useRouter } from 'vue-router';
import { fetchEnrollRecordList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import DateFormat from '@/components/common/date/date-format.vue';
import TableSearch from './modules/table-search.vue';

const router = useRouter();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchEnrollRecordList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 20,
    name: null,
    phone: null,
    id_card_number: null,
    course_title: null,
    status: null
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      align: 'center',
    },
    {
      key: 'student_name',
      title: '姓名',
      align: 'center',
      render: (row) => row.student?.name || '-'
    },
    {
      key: 'student_phone',
      title: '手机号',
      align: 'center',
      render: (row) => row.student?.phone || '-'
    },
    {
      key: 'student_id_card',
      title: '身份证号',
      align: 'center',
      render: (row) => row.student?.id_card_number || '-'
    },
    {
      key: 'course_title',
      title: '报名课程',
      align: 'left',
      render: (row) => row.enroll_config?.title || '-'
    },
    {
      key: 'course_amount',
      title: '报名价格',
      align: 'center',
      render: (row) => row.enroll_config?.amount ? `¥${row.enroll_config.amount}` : '-'
    },
    {
      key: 'status',
      title: '报名状态',
      align: 'center',
      render: (row) => {
        const statusMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'warning',
          1: 'success',
          2: 'error'
        };
        return (
          <NTag type={statusMap[row.status]} bordered={false}>
            {row.status_label}
          </NTag>
        );
      }
    },
    {
      key: 'actions',
      title: $t('common.action'),
      align: 'center',
      render: (row) => (
        <NSpace size={8} justify="center">
          <NButton
            size="small"
            type={row.status === 0 || row.status === 4 ? "primary" : "info"}
            onClick={() => handleDetail(row.id)}
          >
            {row.status === 0 || row.status === 4 ? '审核' : '详情'}
          </NButton>
        </NSpace>
      )
    }
  ]
});

// 处理详情
function handleDetail(id: number) {
  router.push(`/enroll/record-detail?id=${id}`);
}

// 刷新数据
function handleRefresh() {
  getData();
}

onMounted(() => {
  // getData();
});
</script>

<template>
  <div class="flex-col-stretch gap-16px lt-sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="报名记录" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <NButton size="small" @click="handleRefresh" :loading="loading">
          <template #icon>
            <icon-ic-round-refresh class="text-icon" />
          </template>
          {{ $t('common.refresh') }}
        </NButton>
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :scroll-x="1000"
        :loading="loading"
        :row-key="row => row.id"
        :pagination="mobilePagination"
        remote
        size="small"
        class="sm:h-full"
      />
    </NCard>

  </div>
</template>

<style scoped></style>
