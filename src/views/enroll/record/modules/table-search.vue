<script setup lang="ts">
import { NCard, NCollapse, NCollapseItem, NForm, NGrid, NFormItemGi, NInput, NSelect, NSpace, NButton } from 'naive-ui';
import { $t } from '@/locales';
import { enrollRecordStatusOptions } from '@/constants/business';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type SearchParams = {
  name?: string;
  phone?: string;
  id_card_number?: string;
  course_title?: string;
  status?: number;
}

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', { required: true });

async function reset() {
  emit('reset');
}

async function search() {
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse :default-expanded-names="['search']">
      <NCollapseItem :title="$t('common.search')" name="search">
        <NForm :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:4" label="姓名" class="pr-24px">
              <NInput v-model:value="model.name" placeholder="请输入姓名" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:4" label="手机号" class="pr-24px">
              <NInput v-model:value="model.phone" placeholder="请输入手机号" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:4" label="身份证号" class="pr-24px">
              <NInput v-model:value="model.id_card_number" placeholder="请输入身份证号" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:4" label="报名课程" class="pr-24px">
              <NInput v-model:value="model.course_title" placeholder="请输入课程名称" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:4" label="审核状态" class="pr-24px">
              <NSelect v-model:value="model.status" :options="enrollRecordStatusOptions" placeholder="请选择审核状态" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:4" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
