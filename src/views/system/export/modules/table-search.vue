<script setup lang="tsx">
import { NDatePicker } from 'naive-ui';
import { $t } from '@/locales';
import { exportStatusOptions, exportTypeOptions } from '@/constants/business';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type SearchParams = {
  created_at?: [];
  status?: number;
  type?: string;
};

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', { required: true });

async function reset() {
  emit('reset');
}

async function search() {
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse :default-expanded-names="['search']">
      <NCollapseItem :title="$t('common.search')" name="search">
        <NForm :model="model" label-placement="left" label-width="80">
          <NGrid responsive="screen" item-responsive cols="4" x-gap="12">
            <NFormItemGi span="24 s:12 m:1" label="导出时间">
              <NDatePicker v-model:value="model.created_at" type="daterange" clearable value-type="number" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:1" label="导出状态">
              <NSelect v-model:value="model.status" :options="exportStatusOptions" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:1" label="导出类型">
              <NSelect v-model:value="model.type" :options="exportTypeOptions" clearable />
            </NFormItemGi>

            <NFormItemGi span="24 s:12 m:1">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
