<script setup lang="tsx">
import type { TagProps } from 'naive-ui';
import { NButton, NCard, NDataTable, NIcon, NPopconfirm, NSpace, NTag, NTooltip } from 'naive-ui';
import { WarningOutlined } from '@vicons/antd';
import { downloadExport, fetchExportList, removeExport } from '@/service/api';
import { exportStatusRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { showErrorMessage } from '@/utils/message';
import DateFormat from '@/components/common/date/date-format.vue';
import TableSearch from './modules/table-search.vue';

const {
  columns,
  columnChecks,
  data: exportData,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchExportList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    type: null,
    status: null,
    created_at: null
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      align: 'center',
      width: 50
    },
    {
      key: 'desc',
      title: '报表类型',
      align: 'center',
      width: 400,
      fixed: 'left'
    },
    {
      key: 'status',
      title: '导出状态',
      align: 'center',
      width: 100,
      render: row => {
        // 使用映射表来简化状态类型和标签的获取
        const statusMap: Record<number, { type: TagProps['type']; label: string }> = {
          0: { type: 'info', label: exportStatusRecord[0] },
          1: { type: 'success', label: exportStatusRecord[1] },
          2: { type: 'error', label: exportStatusRecord[2] },
          3: { type: 'warning', label: exportStatusRecord[3] }
        };
        const { type, label } = statusMap[row.status] || { type: 'default', label: '未知状态' };
        const isError = row.status === 2 || row.status === 3;

        return (
          <span
            onClick={() => {
              if (isError) {
                window.$dialog?.error({
                  title: '失败原因',
                  positiveText: '确定',
                  content: row.error
                });
              }
            }}
            class={isError ? 'cursor-pointer' : ''}
          >
            <NTooltip disabled={!isError} placement="top">
              {{
                trigger: () => (
                  <NTag type={type} size="small">
                    {label}
                  </NTag>
                ),
                default: () =>
                  (row.status === 2 || row.status === 3) && (
                    <span class="inline-flex items-center gap-4px">
                      <NIcon size="14">
                        <WarningOutlined />
                      </NIcon>
                      <span>点击查看失败原因</span>
                    </span>
                  )
              }}
            </NTooltip>
          </span>
        );
      }
    },
    {
      key: 'created_at',
      title: '导出开始时间',
      align: 'center',
      width: 180,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '导出结束时间',
      align: 'center',
      width: 180,
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'file',
      title: '文件名',
      align: 'center',
      width: 300,
      render: row => {
        return row.attachment?.filename;
      }
    },
    {
      key: 'admin_id',
      title: '导出用户',
      width: 100,
      align: 'center',
      render: row => {
        return row.admin?.username;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 150,
      fixed: 'right',
      render: row => (
        <NSpace size="small" justify="center">
          <NButton
            type="primary"
            size="small"
            onClick={() => download(row)}
            disabled={row.status !== 1 && row.status !== 3}
          >
            下载
          </NButton>
          <NPopconfirm onPositiveClick={() => remove(row)}>
            {{
              default: () => '确定要删除记录吗？',
              trigger: () => (
                <NButton size={'small'} type={'error'}>
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </NSpace>
      )
    }
  ]
});

const { checkedRowKeys, onDeleted } = useTableOperate(exportData, getData);

async function remove(row: Api.Org.Export) {
  await removeExport(row.id);

  await onDeleted();
}

async function download(row: Api.Org.Export) {
  const { error, data } = await downloadExport(row.id);
  if (error) {
    return;
  }

  // 使用fetch API下载文件，避免混合内容问题
  try {
    const response = await fetch(data.url);
    const blob = await response.blob();
    const objectUrl = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = objectUrl;
    link.setAttribute('download', row.attachment?.filename || '导出文件');
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(objectUrl);
  } catch (err: unknown) {
    showErrorMessage(`下载文件失败，错误信息：${(err as Error).message}`);
  }
}
</script>

<template>
  <div class="flex-col-stretch gap-16px lt-sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="导出记录" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          :show-batch-delete="false"
          :show-add="false"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="exportData"
        :loading="loading"
        :row-key="row => row.id"
        :pagination="mobilePagination"
        remote
        size="small"
        table-layout="auto"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
