<script setup lang="tsx">
import { ref } from 'vue';
import { setDefaultTemplate } from '@/service/api';

interface Props {
  row: Api.Org.Template;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const active = defineModel<boolean>('active', {
  default: false
});

const loading = ref<boolean>(false);

async function handleUpdateValue() {
  loading.value = true;

  const { error } = await setDefaultTemplate(props.row.id);

  loading.value = false;

  if (error) {
    return;
  }

  emit('reload');
}
</script>

<template>
  <NSwitch
    :rubber-band="false"
    :value="active"
    :loading="loading"
    :disabled="!row.id"
    @update:value="handleUpdateValue"
  />
</template>

<style scoped></style>
