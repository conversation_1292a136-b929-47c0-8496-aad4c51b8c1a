<script setup lang="tsx">
import { ref } from 'vue';
import { NButton } from 'naive-ui';
import { templateTypeRecord } from '@/constants/business';
import type { DataTableColumns, DataTableRowData } from "naive-ui";
import { getTemplateFields } from '@/service/api';
import { useClipboard } from "@vueuse/core";
import { clipboard } from '@/utils/common';
import { showSuccessMessage, showWaningMessage } from '@/utils/message';

const visible = ref<boolean>(false);
const title = ref<string>('模板使用说明');

const templateType = ref<Api.Org.TemplateType>('hour_cert');
const tableList = ref<DataTableRowData[]>([]);

const columns: DataTableColumns = [
  {
    key: 'label',
    title: '字段',
    align: 'left',
  },
  {
    key: 'field',
    title: '占位符',
    align: 'left',
  },
  {
    key: 'desc',
    title: '描述',
    align: 'left',
  },
  {
    key: 'operate',
    title: '操作',
    align: 'center',
    render: row => {
      if (row.field) {
        return <NButton size="small" text type="primary" onClick={() => handleCopy(row.field as string)}>复制</NButton>;
      } else {
        return '-';
      }
    }
  }
];

const { copy, isSupported } = useClipboard();

async function handleCopy(text: string) {
  if (!isSupported.value) {
    showWaningMessage('您的浏览器不支持Clipboard API');
    return;
  }
  if (!text) {
    showWaningMessage('请输入要复制的内容');
    return;
  }
  await copy(text);
  showSuccessMessage(`复制成功`);
}

async function init() {
  const { error, data } = await getTemplateFields();

  if (error) {
    return;
  }

  tableList.value = data[templateType.value];

  visible.value = true;
}

function open(type: Api.Org.TemplateType) {
  title.value = `【${templateTypeRecord[type]}】模板使用说明`;

  templateType.value = type;
  tableList.value = [];

  init();
}

defineExpose({
  open
})
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="920">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NDataTable
        :columns="columns"
        :data="tableList"
        :row-key="row => row.id"
        remote
        size="small"
        class="mt-3"
      />
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
