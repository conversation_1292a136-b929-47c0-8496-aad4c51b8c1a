<script setup lang="ts">
import { ref } from "vue";
import { $t } from '@/locales';
import { ArrowDropDownFilled } from '@vicons/material';
import { templateTypeOptions } from '@/constants/business';
import TableTemplateFields from "./table-template-fields.vue";

interface Props {
  loading?: boolean;
}

defineProps<Props>();

interface Emits {
  (e: 'add'): void;
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<NaiveUI.TableColumnCheck[]>('columns', {
  default: () => []
});

const fieldsRef = ref();

function add() {
  emit('add');
}

function refresh() {
  emit('refresh');
}

function handleSelect(key: string) {
  fieldsRef.value.open(key as Api.Org.TemplateType);
}
</script>

<template>
  <NSpace wrap justify="end" class="lt-sm:w-200px">
    <slot name="default">
      <NSpace>
        <n-dropdown
          :options="templateTypeOptions"
          key-field="value"
          placement="bottom-start"
          trigger="click"
          @select="handleSelect"
        >
          <n-button size="small" icon-placement="right">
            <template #icon>
              <ArrowDropDownFilled />
            </template>
            模板字段
          </n-button>
        </n-dropdown>

        <NButton size="small" ghost type="primary" @click="add">
          <template #icon>
            <icon-ic-round-plus class="text-icon" />
          </template>
          {{ $t('common.add') }}
        </NButton>
      </NSpace>
    </slot>
    <TableColumnSetting v-model:columns="columns" />
    <slot name="suffix"></slot>

    <TableTemplateFields ref="fieldsRef" />
  </NSpace>
</template>

<style scoped></style>
