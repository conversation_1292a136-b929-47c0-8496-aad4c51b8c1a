<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { createTemplate, updateTemplate } from '@/service/api';
import { useLoading } from '@sa/hooks';
import { templateTypeOptions } from '@/constants/business';
import UploadFile from "@/components/common/upload/upload-file.vue";


interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Org.Template | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const { loading, startLoading, endLoading } = useLoading();


const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增模板',
    edit: '编辑模板',
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.Org.Template,
  'name' | 'tpl_path'
> & {
  type: Api.Org.TemplateType | null;
};

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    type: null,
    tpl_path: '',
  };
}

const rules: Record<string, App.Global.FormRule> = {
  name: defaultRequiredRule,
  type: defaultRequiredRule,
  tpl_path: defaultRequiredRule,
};

function handleInitModel() {
  if (props.operateType === 'add') {
    model.value = createDefaultModel();
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {

    Object.assign(model.value, {
      name: props.rowData.name,
      type: props.rowData.type,
      tpl_path: props.rowData.tpl_path,
    });
    return;
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  let response;

  switch(props.operateType) {
    case 'add':
      response = await createTemplate(model.value);
      break;
    case 'edit':
      response = await updateTemplate(props.rowData?.id as number, model.value);
      break;
  }

  endLoading();

  if (!response.error) {
    window.$message?.success('操作成功');

    emit('submitted');

    closeDrawer();
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="620">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="120">
        <NFormItem label="模板名称" path="name" class="w-320px">
          <NInput v-model:value="model.name" placeholder="请输入模板名称" />
        </NFormItem>
        <NFormItem label="模板类型" path="type" class="w-240px">
          <NSelect v-model:value="model.type" :options="templateTypeOptions" :disabled="!!rowData?.id" />
        </NFormItem>
        <NFormItem label="上传模板文件" path="tpl_path" class="w-480px">
          <UploadFile
              v-model:value="model.tpl_path"
              :preview="rowData?.tpl_path_url"
              :accept="['docx']"
              drag
              storage="priv"
              tips="请上传.docx格式的模板文件"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit" :disabled="loading">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
