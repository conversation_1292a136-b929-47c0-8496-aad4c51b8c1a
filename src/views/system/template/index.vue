<script setup lang="tsx">
import { NButton, NTag, NSpace, NPopconfirm } from 'naive-ui';
import { fetchTemplateList, removeTemplate } from '@/service/api';
import { templateTypeRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { openWebUrl } from "@/utils/common";
import DateFormat from '@/components/common/date/date-format.vue';
import TableOperate from './modules/table-operate.vue';
import TableSearch from './modules/table-search.vue';
import TableRowDefault from "./modules/table-row-default.vue";
import TableHeaderOperation from './modules/table-header-operation.vue';


const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchTemplateList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 100,
    type: null,
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      align: 'center',
      render: row => {
        return row.id ? row.id : '-';
      }
    },
    {
      key: 'name',
      title: '名称',
      align: 'center',
    },
    {
      key: 'type',
      title: '类型',
      align: 'center',
      render: row => {
        const label = templateTypeRecord[row.type];

        const typeMap: Record<Api.Org.TemplateType, NaiveUI.ThemeColor> = {
          hour_cert: 'info',
          student_archive: 'success',
          hour_record: 'warning',
          org_enrollment_form: 'primary',
        };

        return (
          <NTag type={typeMap[row.type]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'is_default',
      title: '是否默认',
      align: 'center',
      render: row => {
        return <TableRowDefault row={row} v-model:active={row.is_default} onReload={() => getData()} />
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      render: row => {
        if (row.id) {
          return (
            <NSpace size="small" justify="center">
              <NButton type="primary" size="small" onClick={() => openWebUrl(row?.tpl_path_url as string)}>
                下载
              </NButton>
              <NButton type="primary" size="small" onClick={() => edit(row)}>
                编辑
              </NButton>
              <NPopconfirm onPositiveClick={() => remove(row)}>
                {{
                  default: () => '确定要删除吗？',
                  trigger: () => (
                    <NButton size={'small'} type={'error'}>
                      删除
                    </NButton>
                  )
                }}
              </NPopconfirm>
            </NSpace>
          );
        } else {
          return (
            <NSpace size="small" justify="center">
              <NButton type="primary" size="small" onClick={() => openWebUrl(row?.tpl_path_url as string)}>
                下载
              </NButton>
            </NSpace>
          );
        }
      }
    }
  ]
});

const {
  drawerVisible,
  editingData,
  operateType,
  handleAdd,
  handleOperate,
  onDeleted
} = useTableOperate(data, getData);

function edit(row: Api.Org.Template) {
  handleOperate('edit', row);
}

async function remove(row: Api.Org.Template) {
  await removeTemplate(row.id);

  await onDeleted();
}
</script>

<template>
  <div class="flex-col-stretch gap-16px lt-sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="模版列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :scroll-x="1200"
        :loading="loading"
        :row-key="row => row.id"
        remote
        size="small"
        table-layout="fixed"
        class="sm:h-full"
      />
      <TableOperate
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
