<script setup lang="tsx">
import { N<PERSON><PERSON><PERSON>, NPopconfirm, NTag, NSpace } from 'naive-ui';
import { fetchAdminList, updateAdmin } from '@/service/api';
import { enableStatusRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import DateFormat from '@/components/common/date/date-format.vue';
import TableOperate from './modules/table-operate.vue';
import TableSearch from './modules/table-search.vue';

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchAdminList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    status: null,
    username: null
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      align: 'center',
      width: 100
    },
    {
      key: 'username',
      title: '用户名',
      align: 'center',
      width: 120
    },
    {
      key: 'real_name',
      title: '姓名',
      align: 'center',
      width: 100
    },
    {
      key: 'phone',
      title: '手机号码',
      align: 'center',
      width: 150
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning'
        };

        const label = enableStatusRecord[row.status];

        return (
          <NTag type={tagMap[row.status]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'roles',
      title: '角色',
      align: 'center',
      width: 150,
      render: row => (
        <NSpace justify="center">
          {row.roles?.map(item => {
            return (
              <NTag type="info" bordered={false}>
                {item.role?.name}
              </NTag>
            );
          })}
        </NSpace>
      )
    },
    {
      key: 'last_active_at',
      title: '最后登录时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.last_active_at} />;
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 180,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row, 'edit')}>
            修改信息
          </NButton>
          <NButton type="warning" ghost size="small" onClick={() => edit(row, 'password')}>
            修改密码
          </NButton>
          {row.status === 0 && (
            <NPopconfirm onPositiveClick={() => handleUpdateStatus(row.id, 1)}>
              {{
                default: () => '确认启用',
                trigger: () => (
                  <NButton size={'small'} ghost type={'info'}>
                    启用
                  </NButton>
                )
              }}
            </NPopconfirm>
          )}

          {row.status === 1 && (
            <NPopconfirm onPositiveClick={() => handleUpdateStatus(row.id, 0)}>
              {{
                default: () => '确认禁用',
                trigger: () => (
                  <NButton size={'small'} ghost type={'error'} disabled={row.is_main === 1}>
                    禁用
                  </NButton>
                )
              }}
            </NPopconfirm>
          )}
        </div>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleOperate
} = useTableOperate(data, getData);

function edit(row: Api.Manage.Admin, type: 'edit' | 'password') {
  handleOperate(type, row);
}

async function handleUpdateStatus(rowId: number, status: number) {
  await updateAdmin(rowId, { status });

  await getData();
}
</script>

<template>
  <div class="flex-col-stretch gap-16px lt-sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="账户列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :show-batch-delete="false"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <TableOperate
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
