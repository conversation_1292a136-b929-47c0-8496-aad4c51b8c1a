<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { createAdmin, updateAdmin, updateAdminPassword } from '@/service/api';
import { useLoading } from '@sa/hooks';
import FormSelectRoles from "./form-select-roles.vue";

type AdminOperateType = 'add' | 'edit' | 'password';

interface Props {
  /** the type of operation */
  operateType: AdminOperateType;
  /** the edit row data */
  rowData?: Api.Manage.Admin | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const { loading, startLoading, endLoading } = useLoading();


const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<AdminOperateType, string> = {
    add: '新增账户',
    edit: '编辑账户信息',
    password: '修改账号密码',
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.Manage.Admin,
  'username' | 'real_name' | 'phone' | 'email'
> & {
  status: number;
  password: string;
  password_confirmation: string;
  role_ids: number[]
};

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    username: '',
    real_name: '',
    phone: '',
    email: '',
    status: 1,
    password: '',
    password_confirmation: '',
    role_ids: []
  };
}

let rules: Record<string, App.Global.FormRule> = {};

function handleInitModel() {
  if (props.operateType === 'add') {
    rules = {
      username: defaultRequiredRule,
      password: defaultRequiredRule,
      real_name: defaultRequiredRule,
      password_confirmation: defaultRequiredRule,
      role_ids: defaultRequiredRule
    };

    model.value = createDefaultModel();
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    rules = {
      real_name: defaultRequiredRule,
      role_ids: defaultRequiredRule
    };

    let roleIds: number[] = [];

    if(props.rowData?.roles && props.rowData.roles.length > 0) {
      props.rowData.roles.forEach(item => {
        roleIds.push(item.role_id);
      });
    }

    Object.assign(model.value, {
      username: props.rowData.username,
      real_name: props.rowData.real_name,
      phone: props.rowData.phone,
      email: props.rowData.email,
      status: props.rowData.status,
      password: '',
      password_confirmation: '',
      role_ids: roleIds
    });
    return;
  }

  if (props.operateType === 'password' && props.rowData) {
    rules = {
      password: defaultRequiredRule,
      password_confirmation: defaultRequiredRule
    };

    Object.assign(model.value, { ...createDefaultModel(), username: props.rowData.username });
    return;
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  startLoading();

  let response;

  switch(props.operateType) {
    case 'add':
      response = await createAdmin(model.value);
      break;
    case 'edit':
      response = await updateAdmin(props.rowData?.id as number, model.value);
      break;
    case 'password':
      response = await updateAdminPassword(props.rowData?.id as number, model.value);
      break;
  }

  endLoading();

  if (!response.error) {
    window.$message?.success('操作成功');
    closeDrawer();
    emit('submitted');
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="520">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="props.operateType === 'add'" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80">
        <NFormItem label="用户名" path="username" class="w-320px">
          <NInput v-model:value="model.username" />
        </NFormItem>
        <NFormItem label="姓名" path="real_name" class="w-220px">
          <NInput v-model:value="model.real_name" />
        </NFormItem>
        <NFormItem label="手机号码" path="phone" class="w-320px">
          <NInput v-model:value="model.phone" />
        </NFormItem>
        <NFormItem label="邮箱地址" path="email" class="w-320px">
          <NInput v-model:value="model.email" />
        </NFormItem>
        <NFormItem label="密码" path="password" class="w-320px">
          <NInput type="password"  v-model:value="model.password" />
        </NFormItem>
        <NFormItem label="确认密码" path="password_confirmation" class="w-320px">
          <NInput type="password"  v-model:value="model.password_confirmation" />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio :value="1" label="开启" />
            <NRadio :value="0" label="禁用" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="角色" path="role_ids">
          <FormSelectRoles v-if="visible" v-model:role_ids="model.role_ids" />
        </NFormItem>
      </NForm>
      <NForm v-else-if="props.operateType === 'edit'" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80">
        <NFormItem label="用户名" path="username" class="w-320px">
          <NInput v-model:value="model.username" :disabled="true" />
        </NFormItem>
        <NFormItem label="姓名" path="real_name" class="w-220px">
          <NInput v-model:value="model.real_name" />
        </NFormItem>
        <NFormItem label="手机号码" path="phone" class="w-320px">
          <NInput v-model:value="model.phone" />
        </NFormItem>
        <NFormItem label="邮箱地址" path="email" class="w-320px">
          <NInput v-model:value="model.email" />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio :value="1" label="开启" />
            <NRadio :value="0" label="禁用" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="角色" path="role_ids">
          <FormSelectRoles v-if="visible" v-model:role_ids="model.role_ids" />
        </NFormItem>
      </NForm>
      <NForm v-else ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="80">
        <NFormItem label="用户名" path="username" class="w-320px">
          <NInput v-model:value="model.username" :disabled="true" />
        </NFormItem>
        <NFormItem label="密码" path="password" class="w-320px">
          <NInput type="password"  v-model:value="model.password" />
        </NFormItem>
        <NFormItem label="确认密码" path="password_confirmation" class="w-320px">
          <NInput type="password"  v-model:value="model.password_confirmation" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit" :disabled="loading">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
