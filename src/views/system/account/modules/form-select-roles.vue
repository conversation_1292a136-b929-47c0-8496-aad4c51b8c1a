<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { fetchGetAllRoles } from '@/service/api';

const roleIds = defineModel<number[]>('role_ids', {
  required: true,
  default: []
});

const roles = ref<Api.Manage.Role[]>([]);

async function init() {
  const { data, error } = await fetchGetAllRoles({ status: 1 });
  if (error) {
    return;
  }

  roles.value = data;
}

onMounted(() => {
  init();
})
</script>

<template>
  <NSelect v-if="roles.length > 0" v-model:value="roleIds" multiple :options="roles" label-field="name" value-field="id" />
</template>

<style scoped></style>
