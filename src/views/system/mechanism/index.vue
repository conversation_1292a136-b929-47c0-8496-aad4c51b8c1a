<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { useLoading } from '@sa/hooks';
import type { CascaderOption } from 'naive-ui';
import { showWaningMessage } from "@/utils/message";
import areaLevel from '@province-city-china/level';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { getOrg, updateOrg } from '@/service/api';
import { useTabStore } from '@/store/modules/tab';

const { loading, startLoading, endLoading } = useLoading();
const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const tabStore = useTabStore();

type Model = Pick<
  Api.Org.Org,
  | 'name'
  | 'alias'
  | 'logo'
  | 'business_license'
  | 'business_scope'
  | 'contact'
  | 'service_qrcode'
  | 'official_seal_image'
  | 'enable_enroll'
  | 'enroll_bg_img'
> & {
  area_code?: string;
  area_text?: string[];
};

const model = ref<Model>({
  name: '',
  alias: '',
  logo: '',
  business_license: '',
  business_scope: '',
  contact: '',
  service_qrcode: '',
  area_code: undefined,
  area_text: [],
  official_seal_image: '',
  enable_enroll: false,
  enroll_bg_img: ''
});

const detail = ref<Api.Org.Org | null>(null);
const pageLoading = ref<boolean>(true);

const rules: Record<string, App.Global.FormRule> = {
  service_qrcode: defaultRequiredRule,
  official_seal_image: defaultRequiredRule,
  contact: defaultRequiredRule,
  area_code: defaultRequiredRule
};

function changeAreaValue(value: string, option: CascaderOption, pathValues: CascaderOption[]) {
  const areaText: string[] = [];

  pathValues.forEach(item => {
    areaText.push(item.name as string);
  });

  model.value.area_text = areaText;
}

async function closePage() {
  await tabStore.removeTabByRouteName('system_mechanism');
}

async function handleSubmit() {

  model.value.business_scope = model.value.business_scope ? model.value.business_scope : '';
  model.value.business_license = model.value.business_license ? model.value.business_license : '';
  model.value.official_seal_image = model.value.official_seal_image ? model.value.official_seal_image : '';
  model.value.service_qrcode = model.value.service_qrcode ? model.value.service_qrcode : '';
  model.value.logo = model.value.logo ? model.value.logo : '';

  startLoading();
  await validate();


  console.log(model.value)
  const { error } = await updateOrg(model.value);

  endLoading();

  if (error) {
    return;
  }

  window.$message?.success('操作成功');
}

async function init() {
  const { error, data } = await getOrg();

  if (error) {
    showWaningMessage('参数错误');
    await closePage();
    return;
  }

  Object.assign(model.value, {
    name: data.name,
    alias: data.alias,
    logo: data.logo || '',
    business_license: data.business_license || '',
    business_scope: data.business_scope || '',
    contact: data.contact,
    service_qrcode: data.service_qrcode || '',
    area_code: data.area_code ? data.area_code.toString() : undefined,
    area_text: data.area_text,
    official_seal_image: data.official_seal_image || '',
    enable_enroll: data.enable_enroll || false,
    enroll_bg_img: data.enroll_bg_img || ''
  });

  detail.value = data;
  pageLoading.value = false;
}

onMounted(async () => {
  await init();
});
</script>

<template>
  <NSpace vertical :size="16">
    <NCard title="机构认证信息" :bordered="false">
      <NForm
        v-if="!pageLoading"
        ref="formRef"
        :model="model"
        :rules="rules"
        :style="{ maxWidth: '640px' }"
        class="ml-10"
      >
        <NFormItem path="name" label="机构名称" class="w-80">
          <NInput v-model:value="model.name" :disabled="true" placeholder="请输入机构名称" />
        </NFormItem>

        <NFormItem path="alias" label="机构简称" class="w-80">
          <NInput v-model:value="model.alias" placeholder="请输入机构简称" />
        </NFormItem>

        <NFormItem path="business_license" label="营业执照">
          <UploadImage v-model:value="model.business_license" :preview="detail?.business_license_url" :max="1" />
        </NFormItem>

        <NFormItem path="business_scope" label="业务范围">
          <NInput v-model:value="model.business_scope" :rows="5" type="textarea" />
        </NFormItem>

        <NFormItem path="contact" label="联系方式" class="w-80">
          <NInput v-model:value="model.contact" placeholder="请输入联系方式" />
        </NFormItem>

        <NFormItem path="area_code" label="所在地" class="w-80">
          <NCascader
            v-model:value="model.area_code"
            :options="areaLevel"
            placeholder="请选择所在地区"
            clearable
            label-field="name"
            value-field="code"
            check-strategy="child"
            @update:value="changeAreaValue"
          />
        </NFormItem>

        <NFormItem path="logo" label="企业LOGO">
          <NSpace vertical>
            <UploadImage v-model:value="model.logo" :preview="detail?.logo_url" :max="1" />
            <FormTips :tips="['注：建议尺寸 100*100，PNG格式图片']" />
          </NSpace>
        </NFormItem>

        <NFormItem v-if="model.enable_enroll" path="enroll_bg_img" label="报名系统背景图">
          <NSpace vertical>
            <UploadImage v-model:value="model.enroll_bg_img" :preview="detail?.enroll_bg_img_url" :max="1" />
            <FormTips :tips="['注：建议尺寸 750*410，PNG/JPG格式图片']" />
          </NSpace>
        </NFormItem>

        <NFormItem path="service_qrcode" label="客服二维码">
          <NSpace vertical>
            <UploadImage v-model:value="model.service_qrcode" :preview="detail?.service_qrcode_url" :max="1" />
            <FormTips :tips="['注：建议尺寸 200*200，PNG格式图片']" />
          </NSpace>
        </NFormItem>

        <NFormItem path="service_qrcode" label="企业公章">
          <NSpace vertical>
            <UploadImage
              v-model:value="model.official_seal_image"
              :preview="detail?.official_seal_image_url"
              :max="1"
            />
            <FormTips :tips="['注：请上传加盖公章的图片，尺寸 200*200，透明PNG格式图片']" />
          </NSpace>
        </NFormItem>

        <NSpace>
          <NButton type="primary" :disabled="loading" @click="handleSubmit">确定</NButton>
          <NButton type="default" @click="closePage">关闭</NButton>
        </NSpace>
      </NForm>
    </NCard>
  </NSpace>
</template>

<style scoped></style>
