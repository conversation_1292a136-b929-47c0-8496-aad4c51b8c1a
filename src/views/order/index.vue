<script setup lang="tsx">
import { NButton, NSpace, NTag, NGrid, NGridItem } from 'naive-ui';
import { fetchOrderList } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { orderStatusRecord, paymentChannelRecord, businessTypeRecord } from '@/constants/business';
import DateFormat from '@/components/common/date/date-format.vue';
import TableHeaderOperation from './modules/table-header-operation.vue';
import TableOperate from './modules/table-operate.vue';
import TableSearch from './modules/table-search.vue';

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchOrderList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    id: null,
    name: null,
    phone: null,
    payment_channel: null,
    business_type: null,
    order_no: null,
    transaction_no: null,
    status: null,
    payment_at: null,
    created_at: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
    },
    {
      key: 'id',
      title: 'ID',
      align: 'center',
    },
    {
      key: 'name',
      title: '学员姓名',
      align: 'center',
    },
    {
      key: 'phone',
      title: '手机号',
      align: 'center',
    },
    {
      key: 'payment_amount',
      title: '支付金额',
      align: 'center',
      render: row => `¥${row.payment_amount}`
    },
    {
      key: 'payment_channel',
      title: '支付渠道',
      align: 'center',
      render: row => paymentChannelRecord[row.payment_channel] || '-'
    },
    {
      key: 'business_type',
      title: '业务类型',
      align: 'center',
      render: row => businessTypeRecord[row.business_type] || row.business_type
    },
    {
      key: 'title',
      title: '已购科目',
      align: 'left',
    },
    {
      key: 'status',
      title: '订单状态',
      align: 'center',
      render: row => {
        const statusMap = {
          0: { type: 'warning', text: orderStatusRecord[0] },
          1: { type: 'success', text: orderStatusRecord[1] },
          2: { type: 'error', text: orderStatusRecord[2] }
        };
        const status = statusMap[row.status] || { type: 'default', text: '未知' };
        return <NTag type={status.type}>{status.text}</NTag>;
      }
    },
    {
      key: 'order_no',
      title: '订单号',
      align: 'center',
    },
    {
      key: 'transaction_no',
      title: '交易号',
      align: 'center',
    },
    {
      key: 'payment_at',
      title: '支付时间',
      align: 'center',
      render: row => row.payment_at ? <DateFormat date={row.payment_at} /> : '-'
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      render: row => <DateFormat date={row.created_at} />
    }
  ]
});

const { drawerVisible, operateType, editingData } = useTableOperate(data, getData);

function handleRefresh() {
  getData();
}

function handleReset() {
  resetSearchParams();
  getDataByPage();
}
</script>

<template>
  <div class="flex-col-stretch gap-16px lt-sm:overflow-auto">
    <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getDataByPage" @reload="getData" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          @refresh="handleRefresh"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :scroll-x="1800"
        :loading="loading"
        :row-key="row => row.id"
        :pagination="mobilePagination"
        remote
        size="small"
        class="sm:h-full"
      />
      <TableOperate
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
