<script setup lang="tsx">
import { $t } from '@/locales';
import TableColumnSetting from '@/components/advanced/table-column-setting.vue';

interface Emits {
  (e: 'refresh'): void;
}

interface Props {
  loading?: boolean;
  disabled?: boolean;
  checkedRowKeys?: string[] | number[];
}

defineProps<Props>();

const emit = defineEmits<Emits>();

const columns = defineModel<NaiveUI.TableColumnCheck[]>('columns', { required: true });

function handleRefresh() {
  emit('refresh');
}
</script>

<template>
  <div class="flex-y-center justify-between">
    <div class="flex-y-center gap-12px">
      <span class="text-base font-medium">订单列表</span>
    </div>
    <div class="flex-y-center gap-12px">
      <NButton size="small" @click="handleRefresh">
        <template #icon>
          <icon-mdi-refresh class="text-icon" />
        </template>
        刷新
      </NButton>
      <TableColumnSetting v-model:columns="columns" />
    </div>
  </div>
</template>

<style scoped></style>
