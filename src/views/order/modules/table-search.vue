<script setup lang="tsx">
import { NDatePicker } from 'naive-ui';
import { $t } from '@/locales';
import { paymentChannelOptions, businessTypeOptions, orderStatusOptions } from '@/constants/business';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
  (e: 'reload'): void;
}

type SearchParams = {
  id?: number;
  name?: string;
  phone?: string;
  payment_channel?: string;
  business_type?: string;
  order_no?: string;
  transaction_no?: string;
  status?: string;
  payment_at?: [];
  created_at?: [];
};

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', { required: true });

async function reset() {
  emit('reset');
}

async function search() {
  emit('search');
}

async function reload() {
  emit('reload');
}
</script>

<template>
  <NCard :title="$t('common.search')" :bordered="false" size="small" class="card-wrapper">
    <NForm :model="model" label-placement="left" :label-width="80">
      <NGrid :cols="24" :x-gap="18">
        <NFormItemGridItem :span="6" label="订单ID" path="id">
          <NInputNumber v-model:value="model.id" clearable placeholder="请输入订单ID" />
        </NFormItemGridItem>
        <NFormItemGridItem :span="6" label="用户姓名" path="name">
          <NInput v-model:value="model.name" clearable placeholder="请输入用户姓名" />
        </NFormItemGridItem>
        <NFormItemGridItem :span="6" label="手机号码" path="phone">
          <NInput v-model:value="model.phone" clearable placeholder="请输入手机号码" />
        </NFormItemGridItem>
        <NFormItemGridItem :span="6" label="支付渠道" path="payment_channel">
          <NSelect
            v-model:value="model.payment_channel"
            clearable
            placeholder="请选择支付渠道"
            :options="paymentChannelOptions"
          />
        </NFormItemGridItem>
        <NFormItemGridItem :span="6" label="业务类型" path="business_type">
          <NSelect
            v-model:value="model.business_type"
            clearable
            placeholder="请选择业务类型"
            :options="businessTypeOptions"
          />
        </NFormItemGridItem>
        <NFormItemGridItem :span="6" label="订单编号" path="order_no">
          <NInput v-model:value="model.order_no" clearable placeholder="请输入订单编号" />
        </NFormItemGridItem>
        <NFormItemGridItem :span="6" label="交易号" path="transaction_no">
          <NInput v-model:value="model.transaction_no" clearable placeholder="请输入交易号" />
        </NFormItemGridItem>
        <NFormItemGridItem :span="6" label="订单状态" path="status">
          <NSelect
            v-model:value="model.status"
            clearable
            placeholder="请选择订单状态"
            :options="orderStatusOptions"
          />
        </NFormItemGridItem>
        <NFormItemGridItem :span="6" label="支付时间" path="payment_at">
          <NDatePicker
            v-model:value="model.payment_at"
            type="datetimerange"
            clearable
            class="w-full"
            placeholder="请选择支付时间"
          />
        </NFormItemGridItem>
        <NFormItemGridItem :span="6" label="创建时间" path="created_at">
          <NDatePicker
            v-model:value="model.created_at"
            type="datetimerange"
            clearable
            class="w-full"
            placeholder="请选择创建时间"
          />
        </NFormItemGridItem>
        <NFormItemGridItem :span="4">
          <NSpace class="w-full" justify="end">
            <NButton @click="reset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              {{ $t('common.reset') }}
            </NButton>
            <NButton type="primary" ghost @click="search">
              <template #icon>
                <icon-ic-round-search class="text-icon" />
              </template>
              {{ $t('common.search') }}
            </NButton>
          </NSpace>
        </NFormItemGridItem>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
