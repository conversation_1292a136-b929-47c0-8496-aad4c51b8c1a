<script setup lang="tsx">
import { computed, reactive, watch } from 'vue';
import { $t } from '@/locales';
import { orderStatusOptions } from '@/constants/business';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Org.Order | null;
}

interface Emits {
  (e: 'submitted'): void;
}

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增订单',
    edit: '订单详情'
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Org.Order, 'id' | 'order_no' | 'status'>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: 0,
    order_no: '',
    status: 0
  };
}

function handleUpdateModelByModalType() {
  const handlers: Record<NaiveUI.TableOperateType, () => void> = {
    add: () => {
      const defaultModel = createDefaultModel();
      Object.assign(model, defaultModel);
    },
    edit: () => {
      if (props.rowData) {
        Object.assign(model, props.rowData);
      }
    }
  };

  handlers[props.operateType]();
}

async function handleSubmit() {
  // 这里可以添加提交逻辑
  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

function closeDrawer() {
  visible.value = false;
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelByModalType();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm :model="model">
        <NFormItem label="订单编号" path="order_no">
          <NInput v-model:value="model.order_no" placeholder="请输入订单编号" readonly />
        </NFormItem>
        <NFormItem label="订单状态" path="status">
          <NSelect
            v-model:value="model.status"
            placeholder="请选择订单状态"
            :options="orderStatusOptions"
            disabled
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
