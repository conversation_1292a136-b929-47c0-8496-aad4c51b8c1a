<script setup lang="tsx">
import { useAuth } from '@/hooks/business/auth';
import { RoleCode } from "@/enum";
import RoleAdmin from "./modules/role/admin.vue";
import RoleGuest from "./modules/role/guest.vue";

const { hasAuth } = useAuth();
</script>

<template>
  <NSpace vertical :size="16">
    <RoleAdmin v-if="hasAuth(RoleCode.Admin) || hasAuth(RoleCode.Auditor)" />
    <RoleGuest v-else />
  </NSpace>
</template>

<style scoped></style>
