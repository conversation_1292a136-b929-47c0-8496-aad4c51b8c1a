<script setup lang="tsx">
import { $t } from '@/locales';
import { balanceRecordTypeOptions } from '@/constants/business';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type SearchParams = {
  type?: number;
  enroll_id?: string,
  created_at?: string[] | null
}

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', { required: true });

async function reset() {
  emit('reset');
}

async function search() {
  emit('search');
}
</script>

<template>
  <NForm :model="model">
    <NGrid responsive="screen" item-responsive>
      <NFormItemGi span="24 s:12 m:4" label="学员ID" class="pr-10px">
        <NInput v-model:value="model.enroll_id" clearable />
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:3" label="类型" class="pr-10px">
        <NSelect v-model:value="model.type" :options="balanceRecordTypeOptions" clearable/>
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:10" label="创建时间" class="pr-10px">
        <DatetimeRange v-model:value="model.created_at" />
      </NFormItemGi>

      <NFormItemGi span="24 s:12 m:5" class="pr-10px">
        <NSpace class="w-full" justify="end">
          <NButton @click="reset">
            <template #icon>
              <icon-ic-round-refresh class="text-icon" />
            </template>
            {{ $t('common.reset') }}
          </NButton>
          <NButton type="primary" ghost @click="search">
            <template #icon>
              <icon-ic-round-search class="text-icon" />
            </template>
            {{ $t('common.search') }}
          </NButton>
        </NSpace>
      </NFormItemGi>
    </NGrid>
  </NForm>
</template>

<style scoped></style>
