<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { fetchClassList } from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';

const { routerPushBy<PERSON>ey } = useRouterPush();

const list = ref<Api.Org.Class[]>([]);
const loading = ref<boolean>(true);

function go(id: string) {
  routerPushByKey('class_detail', { query: { id } });
}

function toRate(count: number, total: number) {
  if (total > 0) {
    return parseFloat(((count / total) * 100).toFixed(2));
  } else {
    return 0;
  }
}

async function init() {
  const { error, data } = await fetchClassList({ current: 1, size: 10, status: 1, type: ['course', 'course_pack'] });

  loading.value = false;

  if (error) {
    return;
  }

  list.value = data.records;
}

onMounted(async () => {
  await init();
})
</script>

<template>
  <NCard title="进行中的班级统计" :bordered="false" class="h-full card-wrapper">
    <NGrid v-if="!loading" :x-gap="16" :y-gap="16">
      <NGi v-if="list.length > 0" :span="12" v-for="(item, index) in list" :key="index">
        <NCard size="small">
          <n-thing>
            <template #header>
              <span class="font-bold">{{ item.name }}</span>
            </template>
            <template  #header-extra>
              <n-button text tag="a" type="primary" @click="go(item.id.toString())">
                查看
              </n-button>
            </template>
            <template  #description>
              <span class="font-bold text-12px">{{ item.resource?.name }}</span>
            </template>
            <div class="class-content pt-4">
              <div class="class-content-item">
                <div class="font-300 text-11px">学员数</div>
                <div class="font-bold text-18px pt-1">{{ item.total_enrollments }}</div>
              </div>
              <div class="class-content-item">
                <div class="font-300 text-11px">应修学时完成率</div>
                <div class="font-bold text-18px mt-1">{{ toRate(item.total_course_finished, item.total_enrollments) }}%</div>
              </div>
              <div class="class-content-item">
                <div class="font-300 text-11px">考试及格率</div>
                <div class="font-bold text-18px mt-1">{{ toRate(item.total_passed, item.total_examined) }}%</div>
              </div>
            </div>
            <template #action>
              <NSpace>
                <ExportButton name="下载班级档案" export-type="student_archive" :export-ids="[item.id]" :text="true" />
                <n-button text tag="a" type="primary" @click="go(item.id.toString())">
                  下载学时证明
                </n-button>
              </NSpace>
            </template>
          </n-thing>
        </NCard>
      </NGi>
      <NGi v-else :span="24">
        <n-empty></n-empty>
      </NGi>
    </NGrid>
  </NCard>
</template>

<style scoped>
.class-content {
  display: flex;
  flex-direction: row;
  height: 100px;
}

.class-content-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 48px;
}
</style>
