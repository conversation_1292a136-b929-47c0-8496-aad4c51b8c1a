<script setup lang="tsx">
import { ref } from 'vue';
import { NSpace, NText, NTag } from 'naive-ui';
import { fetchBalanceRecordList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { balanceRecordTypeRecord } from '@/constants/business';
import DateFormat from '@/components/common/date/date-format.vue';
import TableSearch from './table-balance-record-search.vue';

const visible = ref<boolean>(false);

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchBalanceRecordList,
  showTotal: true,
  immediate: false,
  apiParams: {
    current: 1,
    size: 10,
    type: null,
    enroll_id: null,
    created_at: []
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      align: 'center',
    },
    {
      key: 'enroll_id',
      title: '学员ID',
      align: 'center',
    },
    {
      key: 'amount',
      title: '变更金额',
      align: 'center',
      render: row => {
        const typeMap: Record<Api.Org.BalanceRecordType, NaiveUI.ThemeColor> = {
          income: 'error',
          expense: 'success',
          refund: 'warning',
        };

        if (row.status == 2) {
          return <NText type={typeMap[row.type]} class="line-through">{row.amount > 0 ? `+${row.amount}` : row.amount}</NText>;
        } else {
          return <NText type={typeMap[row.type]}>{row.amount > 0 ? `+${row.amount}` : row.amount}</NText>;
        }
      }
    },
    {
      key: 'origin_balance',
      title: '剩余金额',
      align: 'center',
      render: row => {
        return (parseFloat(row.origin_balance) + parseFloat(row.amount)).toFixed(2);
      }
    },
    {
      key: 'remark',
      title: '备注',
      align: 'center',
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.updated_at} />;
      }
    }
  ]
});

function open() {
  getData();

  visible.value = true;
}

defineExpose({
  open
})
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="1080">
    <NDrawerContent title="余额明细" :native-scrollbar="false" closable>
      <NCard size="small" class="sm:flex-1-hidden card-wrapper">
        <NSpace vertical>
          <TableSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />

          <NDataTable
            :columns="columns"
            :data="data"
            :loading="loading"
            :row-key="row => row.id"
            :pagination="mobilePagination"
            remote
            size="small"
          />
        </NSpace>
      </NCard>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
