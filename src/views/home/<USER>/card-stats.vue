<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { createReusableTemplate } from "@vueuse/core";
import { useRouterPush } from '@/hooks/common/router';
import { ChevronRightRound } from '@vicons/material';
import { getUserInfo } from "@/store/modules/auth/shared";
import { getStatisticSubtotalData } from '@/service/api';
import TableBalanceRecord from "./table-balance-record.vue";

interface CardData {
  key: string;
  title: string;
  decimals?: number;
  value: number;
  today: number;
  yesterday: number;
  unit: string;
  color: {
    start: string;
    end: string;
  };
  icon: string;
}

interface GradientBgProps {
  gradientColor: string;
}

const { routerPushByKey } = useRouterPush();

const userInfo = getUserInfo();
const tableRef = ref();

const cardData = ref<CardData[]>([
  {
    key: 'enrollment_count',
    title: '报名人数',
    value: 0,
    today: 0,
    yesterday: 0,
    unit: '',
    color: {
      start: '#00CC99',
      end: '#00CCFF'
    },
    icon: 'mdi:people'
  },
  {
    key: 'class_count',
    title: '班级数量',
    decimals: 0,
    value: 0,
    today: 0,
    yesterday: 0,
    unit: '',
    color: {
      start: '#865ec0',
      end: '#5144b4'
    },
    icon: 'mingcute:classify-2-line'
  },
  {
    key: 'train_enrollment_count',
    title: '完成培训人数',
    value: 0,
    today: 0,
    yesterday: 0,
    unit: '',
    color: {
      start: '#009966',
      end: '#009999'
    },
    icon: 'mdi:people-check'
  },
  {
    key: 'balance',
    title: '账户余额',
    value: userInfo.org?.balance as number,
    today: 0,
    yesterday: 0,
    unit: '¥',
    color: {
      start: '#fcbc25',
      end: '#f68057'
    },
    icon: 'ant-design:money-collect-outlined'
  }
]);

const [DefineGradientBg, GradientBg] = createReusableTemplate<GradientBgProps>();

function getGradientColor(color: CardData['color']) {
  return `linear-gradient(to bottom right, ${color.start}, ${color.end})`;
}

function openBalanceRecord() {
  tableRef.value.open();
}

async function init() {
  const { error, data } = await getStatisticSubtotalData();

  if (error) {
    return;
  }

  cardData.value[0].value = data.subtotal.enrollments;
  cardData.value[0].today = data.today.enrollments;
  cardData.value[0].yesterday = data.yesterday.enrollments;

  cardData.value[1].value = data.subtotal.classes;
  cardData.value[1].today = data.today.classes;
  cardData.value[1].yesterday = data.yesterday.classes;

  cardData.value[2].value = data.subtotal.trained;
  cardData.value[2].today = data.today.trained;
  cardData.value[2].yesterday = data.yesterday.trained;
}

onMounted(() => {
  init();
});
</script>

<template>
  <NCard :bordered="false" class="h-full card-wrapper">
    <!-- define component start: GradientBg -->
    <DefineGradientBg v-slot="{ $slots, gradientColor }">
      <div class="rd-8px px-16px pb-4px pt-8px text-white" :style="{ backgroundImage: gradientColor }">
        <component :is="$slots.default" />
      </div>
    </DefineGradientBg>
    <!-- define component end: GradientBg -->

    <NGrid cols="s:1 m:2 l:4" responsive="screen" :x-gap="16" :y-gap="16">
      <NGi v-for="item in cardData" :key="item.key">
        <GradientBg :gradient-color="getGradientColor(item.color)" class="flex-1">
          <h3 class="text-16px">{{ item.title }}</h3>
          <div class="flex justify-between pt-12px">
            <SvgIcon :icon="item.icon" class="text-32px" />
            <CountTo
              :prefix="item.unit"
              :start-value="0"
              :end-value="item.value"
              class="text-30px text-white dark:text-dark"
            />
          </div>
          <NSpace v-if="item.key != 'balance'" justify="end" class="stat-txt">
            <div> 昨天：{{ item.yesterday }}</div>
            <div> 今日：{{ item.today }}</div>
          </NSpace>
          <NSpace v-else justify="end">
            <div  class="flex flex-center" @click="openBalanceRecord">
              <n-button text icon-placement="right" color="white" size="small">
                <template #icon>
                  <n-icon>
                    <ChevronRightRound />
                  </n-icon>
                </template>
                使用明细
              </n-button>
            </div>
          </NSpace>
        </GradientBg>
      </NGi>
    </NGrid>

    <n-divider />

    <NGrid :x-gap="16" :y-gap="16">
      <NGi :span="4">
        <NCard hoverable>
          <div class="flex flex-col pt-12px" @click="routerPushByKey('student')">
            <div>
              <SvgIcon icon="hugeicons:student-card" class="text-32px font-bold" />
            </div>
            <div class="font-bold text-18px pt-4">
              学员管理
            </div>
          </div>
        </NCard>
      </NGi>
      <NGi :span="4">
        <NCard hoverable>
          <div class="flex flex-col pt-12px" @click="routerPushByKey('class')">
            <div>
              <SvgIcon icon="mingcute:classify-2-line" class="text-32px font-bold" />
            </div>
            <div class="font-bold text-18px pt-4">
              班级管理
            </div>
          </div>
        </NCard>
      </NGi>
      <NGi :span="4">
        <NCard hoverable>
          <div class="flex flex-col pt-12px" @click="routerPushByKey('enroll_enrollment')">
            <div>
              <SvgIcon icon="fluent:form-28-regular" class="text-32px font-bold" />
            </div>
            <div class="font-bold text-18px pt-4">
              报名配置
            </div>
          </div>
        </NCard>
      </NGi>
    </NGrid>
    <TableBalanceRecord ref="tableRef" />
  </NCard>
</template>

<style scoped>
  .stat-txt {
    padding-top: 8px;
  }
</style>
