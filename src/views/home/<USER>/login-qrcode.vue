<script setup lang="tsx">
import { ref } from 'vue';
import { HelpCircleOutline } from '@vicons/ionicons5';
import { CustomerServiceOutlined } from '@vicons/antd';
import { getUserInfo } from '@/store/modules/auth/shared';
import { openWebUrl, clipboard } from "@/utils/common";

const userInfo = getUserInfo();

const mobileCopyUrl = ref<string>(`
weixin://dl/business/?appid=wx06b2b154c3d298d3&path=packageOrg/organization/home&query=${encodeURIComponent(`org_sid=${userInfo.org?.sid || ''}`)}
`);

const mobileUrl = ref<string>( `${userInfo.config?.org_pc_url}/mp/org/?org_sid=${userInfo.org?.sid}`);

const pcUrl = ref<string>(`${userInfo.config?.org_pc_url}/${userInfo.org?.sid}/home`);
const applyUrl = ref<string>(`${userInfo.config?.org_pc_url}/${userInfo.org?.sid}/apply`);
const helpUrl = 'https://ztuakzhkzg.feishu.cn/docx/G9Xsdjuefo3gjAxu1H7cQYnknWT?from=from_copylink';

async function handleCopy(text: string) {
  await clipboard(text);
}

</script>

<template>
  <NCard title="学员端登录方式" :bordered="false" class="h-full card-wrapper">
    <div>
      <NCard size="small">
        <NTabs default-value="mobile" size="large">
          <NTabPane name="mobile" tab="手机端">
            <Qrcode :value="mobileUrl" :copyUrl="mobileCopyUrl" :width="160" :copy-show="true" :download-show="true" />
          </NTabPane>
          <NTabPane name="apple" tab="报名地址">
            <Qrcode :value="applyUrl" :width="160" :copy-show="true" :download-show="true" />
          </NTabPane>
        </NTabs>
      </NCard>
    </div>
    <div class="pt-5">
      <NCard title="电脑端" size="small">
        <NGrid :y-gap="16">
          <NGi :span="24">
            <div class="pc-url" @click="openWebUrl(pcUrl)">{{ pcUrl }}</div>
          </NGi>
          <NGi :span="24">
            <n-button text tag="a" type="primary" @click="handleCopy(pcUrl)">
              复制链接地址
            </n-button>
          </NGi>
        </NGrid>
      </NCard>
    </div>
    <div class="pt-10 ml-2">
      <NSpace size="large">
        <div class="flex flex-col-center" @click="openWebUrl(helpUrl)">
          <n-icon size="32">
            <HelpCircleOutline />
          </n-icon>
          <span class="help-txt">帮助文档</span>
        </div>
        <div class="flex flex-col-center pl-5" @click="openWebUrl(helpUrl)">
          <n-icon size="32">
            <CustomerServiceOutlined />
          </n-icon>
          <span class="help-txt">联系客服</span>
        </div>
      </NSpace>
    </div>
  </NCard>
</template>

<style scoped>
.pc-url {
  height: 72px;
  width: 100%;
  background-color: #F3F3FF;
  border-radius: 12px;
  padding: 0 24px;
  color: #195EEC;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.help-txt {
  padding-top: 2px;
  font-size: 14px;
  font-weight: bold;
}
</style>
