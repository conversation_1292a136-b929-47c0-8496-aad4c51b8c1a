<script setup lang="tsx">
import { computed } from 'vue';
import { useAppStore } from '@/store/modules/app';
import CardClass from "../card-class.vue";
import CardStats from "../card-stats.vue";
import LoginQrcode from '../login-qrcode.vue';

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));
</script>

<template>
  <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
    <NGi span="24 s:24 m:17">
      <div>
        <CardStats />
      </div>
      <div class="pt-5">
        <CardClass />
      </div>
    </NGi>
    <NGi span="24 s:24 m:7">
      <LoginQrcode />
    </NGi>
  </NGrid>
</template>

<style scoped></style>
