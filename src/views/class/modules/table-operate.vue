<script setup lang="tsx">
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { classTypeOptions } from '@/constants/business'
import { createClass, updateClass, getClassOptions } from '@/service/api';
import { useLoading } from '@sa/hooks';
import { showWaningMessage } from "@/utils/message";
import ManagerTableOperate from '@/views/system/account/modules/table-operate.vue';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Org.Class | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
  (e: 'loaded', o: Api.Org.ClassOption): void;
}

const emit = defineEmits<Emits>();

const { loading, startLoading, endLoading } = useLoading();

const option = ref<Api.Org.ClassOption | null>(null);
const drawerManagerVisible = ref<boolean>(false);
const courseTopic = ref<Api.Train.Topic | null>(null);

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增班级',
    edit: '编辑班级',
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.Org.Class,
  'name' | 'type' | 'exam_enabled' | 'exam_limit' | 'exam_limit_count' | 'exam_condition' |
  'exam_mode' | 'face_capture_enabled' | 'face_capture_count' | 'template_custom'
> & {
  manager_id?: number;
  resource_id: number | null;
  exam_at: string | null;
  template_hour_id: number | null;
  template_archive_id: number | null;
  start_at: string | null;
  end_at: string | null;
  date_at: string[];
};

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    manager_id: option.value?.manager_id,
    type: 'course',
    resource_id: null,
    exam_enabled: true,
    exam_limit: false,
    exam_limit_count: 3,
    exam_condition: 'anytime',
    exam_mode: 'all',
    exam_at: null,
    face_capture_enabled: true,
    face_capture_count: 1,
    template_custom: true,
    template_hour_id: null,
    template_archive_id: null,
    start_at: null,
    end_at: null,
    date_at: []
  };
}

const rules: Record<string, App.Global.FormRule> = {
  name: defaultRequiredRule,
  manager_id: defaultRequiredRule,
  resource_id: defaultRequiredRule,
  date_at: defaultRequiredRule,
};

function closeDrawer() {
  visible.value = false;
}

function openManagerDrawer() {
  drawerManagerVisible.value = true;
}

function handleInitModel() {
  if (props.operateType === 'add') {
    model.value = createDefaultModel();
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, {
      name: props.rowData.name,
      manager_id: props.rowData.manager_id,
      type: props.rowData.type,
      resource_id: props.rowData.resource_id,
      exam_enabled: props.rowData.exam_enabled,
      exam_limit: props.rowData.exam_limit,
      exam_limit_count: props.rowData.exam_limit_count,
      exam_condition: props.rowData.exam_condition,
      exam_mode: props.rowData.exam_mode,
      exam_at: props.rowData.exam_at,
      face_capture_enabled: props.rowData.face_capture_enabled,
      face_capture_count: props.rowData.face_capture_count,
      template_custom: props.rowData.template_custom,
      template_hour_id: props.rowData.template_hour_id ? props.rowData.template_hour_id : null,
      template_archive_id: props.rowData.template_archive_id ? props.rowData.template_archive_id : null,
      date_at: props.rowData.start_at ? [props.rowData.start_at, props.rowData.end_at] : []
    });

    // 课程关联的题库
    let tmpSelectOption = null;
    if (model.value.type == 'course') {
      tmpSelectOption = option.value?.courses.find(item => item.course_id == model.value.resource_id);
    } else if (model.value.type == 'course_pack') {
      tmpSelectOption = option.value?.course_packs.find(item => item.course_pack_id == model.value.resource_id);
    }

    if (tmpSelectOption) {
      handleSelectCourse(0, tmpSelectOption);
    }
    return;
  }
}

async function handleSubmit() {
  await validate();

  if (model.value.exam_condition == 'fixed_time' && !model.value.exam_at) {
    showWaningMessage('请选择考试时间');
    return;
  }

  if (model.value.exam_limit && model.value.exam_limit_count < 1) {
    showWaningMessage('请设置考试次数');
    return;
  }

  if (model.value.date_at.length == 2) {
    model.value.start_at = model.value.date_at[0];
    model.value.end_at = model.value.date_at[1];
  }

  if (!model.value.template_custom) {
    model.value.template_hour_id = 0;
    model.value.template_archive_id = 0;
  }

  model.value.template_hour_id = model.value.template_hour_id ? model.value.template_hour_id : 0;
  model.value.template_archive_id = model.value.template_archive_id ? model.value.template_archive_id : 0;

  startLoading();

  let response;

  switch(props.operateType) {
    case 'add':
      response = await createClass(model.value);
      break;
    case 'edit':
      response = await updateClass(props.rowData?.id as number, model.value);
      break;
  }

  endLoading();

  if (!response.error) {
    window.$message?.success('操作成功');

    emit('submitted');

    closeDrawer();
  }
}

function changeType() {
  courseTopic.value = null;
  model.value.resource_id = null;

  if (model.value.type == 'topic') {
    model.value.exam_enabled = false;
    model.value.face_capture_enabled = false;
    model.value.template_custom = false;
  } else {
    model.value.exam_enabled = true;
    model.value.face_capture_enabled = true;
    model.value.template_custom = true;
  }
}

function handleSelectCourse(value: number, option: any) {
  courseTopic.value = null;

  nextTick(() => {
    if (option.topic) {
      courseTopic.value = option.topic
    }
  })
}

async function init() {
  const { error, data } = await getClassOptions();

  if (error) {
    return;
  }

  option.value = data;

  emit('loaded', option.value);
}

watch(visible, () => {
  if (visible.value) {
    courseTopic.value = null;

    handleInitModel();
    restoreValidation();
  }
});

onMounted(async () => {
  await init();
})
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="720">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="option" ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="120">
        <NFormItem label="班级名称" path="name" class="w-320px">
          <NInput v-model:value="model.name" placeholder="请输入班级名称" />
        </NFormItem>
        <NFormItem v-if="!option.teacher" label="负责老师" path="manager_id" >
          <NSpace>
            <div class="w-200px">
              <NSelect
                v-model:value="model.manager_id"
                :options="option.managers"
                :disabled="option.teacher"
                label-field="real_name"
                value-field="id"
                placeholder="请选择老师"
                clearable
              />
            </div>
            <NButton type="primary" @click="openManagerDrawer">添加老师</NButton>
          </NSpace>
        </NFormItem>
        <NFormItem label="选择科目" path="resource_id" >
          <NFlex vertical>
            <div class="w-500px">
              <n-radio-group v-model:value="model.type" :disabled="operateType == 'edit'" name="type" @update:value="changeType">
                <n-space>
                  <n-radio v-for="item in classTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </n-radio>
                </n-space>
              </n-radio-group>
            </div>

            <div>
              <NSelect
                v-if="model.type == 'course'"
                v-model:value="model.resource_id"
                :options="option.courses"
                :disabled="operateType == 'edit'"
                label-field="name"
                value-field="course_id"
                placeholder="请选择课程"
                clearable
                @update:value="handleSelectCourse"
              />
              <NSelect
                v-else-if="model.type == 'course_pack'"
                v-model:value="model.resource_id"
                :options="option.course_packs"
                :disabled="operateType == 'edit'"
                label-field="name"
                value-field="course_pack_id"
                placeholder="请选择课程包"
                clearable
                @update:value="handleSelectCourse"
              />
              <NSelect
                v-else-if="model.type == 'topic'"
                v-model:value="model.resource_id"
                :options="option.topics"
                :disabled="operateType == 'edit'"
                label-field="name"
                value-field="topic_id"
                placeholder="请选择题库"
                clearable
              />
            </div>
            <div v-if="courseTopic">
              <NInputGroup>
                <n-button type="info" :disabled="true">
                  题库
                </n-button>
                <n-input :value="courseTopic.name" :disabled="true" />
              </NInputGroup>
            </div>
          </NFlex>
        </NFormItem>
        <NFormItem v-if="model.type != 'topic'" label="课程人脸抓拍" path="face_capture_enabled" class="w-320px">
          <NSwitch v-model:value="model.face_capture_enabled" />
        </NFormItem>
        <NFormItem v-if="model.face_capture_enabled" label="设置抓拍次数" path="face_capture_count" class="w-260px">
          <NInputNumber v-model:value="model.face_capture_count" placeholder="请输入次数">
            <template #suffix>
              次
            </template>
          </NInputNumber>
        </NFormItem>
        <NFormItem v-if="model.type != 'topic'"  label="考试" path="exam_enabled">
          <NSpace vertical>
            <div class="w-500px">
              <NSwitch v-model:value="model.exam_enabled" />
            </div>

            <div v-if="model.exam_enabled" class="exam-box">
              <div class="exam-item">
                <div class="exam-item-title">考试次数</div>
                <div class="exam-item-rule">
                  <div>
                    <n-radio-group v-model:value="model.exam_limit" name="exam_limit">
                      <n-radio :value="false">无限次</n-radio>
                      <n-radio :value="true">有限次</n-radio>
                    </n-radio-group>
                  </div>
                  <div class="w-100px">
                    <NInputNumber v-model:value="model.exam_limit_count" />
                  </div>
                </div>
              </div>
              <div class="exam-item">
                <div class="exam-item-title">考试条件</div>
                <div class="exam-item-rule">
                  <div>
                    <n-radio-group v-model:value="model.exam_condition" name="exam_condition">
                      <n-radio value="anytime">随时考</n-radio>
                      <n-radio value="after_course">学时满可考</n-radio>
                      <n-radio value="fixed_time">统一时间考</n-radio>
                    </n-radio-group>
                  </div>
                  <div class="w-130px">
                    <Date v-model:value="model.exam_at" />
                  </div>
                </div>
              </div>
              <div class="exam-item">
                <div class="exam-item-title">考试方式</div>
                <div class="exam-item-rule">
                  <div>
                    <n-radio-group v-model:value="model.exam_mode" name="exam_mode">
                      <n-radio value="all">手机、电脑端均可</n-radio>
                      <n-radio value="pc_only">仅电脑端</n-radio>
                      <n-radio value="mobile_only">仅手机端</n-radio>
                    </n-radio-group>
                  </div>
                </div>
              </div>
            </div>
          </NSpace>
        </NFormItem>
        <NFormItem v-if="model.type != 'topic'" label="下载模板配置" path="template_custom">
          <NSwitch v-model:value="model.template_custom" />
        </NFormItem>
        <NFormItem v-if="model.template_custom" label="学时证明模板" path="template_hour_id" class="w-420px">
          <NSelect
            v-model:value="model.template_hour_id"
            :options="option.template_hours"
            label-field="name"
            value-field="id"
            placeholder="请选择模板"
            clearable
          />
        </NFormItem>
        <NFormItem v-if="model.template_custom" label="一期一档模板" path="template_archive_id" class="w-420px">
          <NSelect
            v-model:value="model.template_archive_id"
            :options="option.template_archives"
            label-field="name"
            value-field="id"
            placeholder="请选择模板"
            clearable
          />
        </NFormItem>
        <NFormItem label="培训时间" path="date_at" class="w-420px">
          <NSpace vertical>
            <DateRange v-model:value="model.date_at" />
            <FormTips :tips="['结束时间自动关闭班级']" />
          </NSpace>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit" :disabled="loading">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>

  <ManagerTableOperate
    v-model:visible="drawerManagerVisible"
    operate-type="add"
    :row-data="null"
    @submitted="init"
  />
</template>

<style scoped>
.exam-box {
  padding: 10px 20px;
  background-color: #F3F3FF;
  border-radius: 12px;
}

.exam-item {
  padding-bottom: 10px;
}

.exam-item-title {
  font-weight: bold;
}

.exam-item-rule {
  display: flex;
  flex-direction: row;
}

.exam-item-rule div {
  line-height: 36px;
}
</style>
