<script setup lang="ts">

interface Props {
  loading?: boolean;
  rowKeys?: number[];
}

withDefaults(defineProps<Props>(), {
  rowKeys: () => []
});

interface Emits {
  (e: 'add'): void;
  (e: 'refresh', clear?: boolean): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<NaiveUI.TableColumnCheck[]>('columns', {
  default: () => []
});

function add() {
  emit('add');
}

function refresh(clear?: boolean) {
  emit('refresh', clear);
}
</script>

<template>
  <NSpace wrap justify="end" class="lt-sm:w-200px">
    <slot name="default">
      <NButton size="small" type="primary" @click="add">
        新增班级
      </NButton>
      <ExportButton name="导出班级档案" export-type="student_archive" :export-ids="rowKeys" :disabled="rowKeys.length === 0" @refresh="refresh(true)" />
    </slot>
    <TableColumnSetting v-model:columns="columns" />
    <slot name="suffix"></slot>
  </NSpace>
</template>

<style scoped></style>
