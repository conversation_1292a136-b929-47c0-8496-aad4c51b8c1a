<script setup lang="tsx">
import { $t } from '@/locales';
import { classStatusOptions, classTypeOptions } from '@/constants/business';

interface Props {
  option?: Api.Org.ClassOption | null;
}

defineProps<Props>();

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type SearchParams = {
  name?: string;
  status?: number;
  type?: string | null;
  resource_id?: number | null;
}

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', {required: true});

async function reset() {
  emit('reset');
}

async function search() {
  emit('search');
}

function changeClassType() {
  model.value.resource_id = null;
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse :default-expanded-names="['search']">
      <NCollapseItem :title="$t('common.search')" name="search">
        <NForm :model="model">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:3" label="名称" class="pr-10px">
              <NInput v-model:value="model.name" clearable/>
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:2" label="状态" class="pr-10px">
              <NSelect v-model:value="model.status" :options="classStatusOptions" clearable/>
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:2" label="科目类型" class="pr-10px">
              <NSelect v-model:value="model.type" :options="classTypeOptions" @update:value="changeClassType" clearable />
            </NFormItemGi>
            <NFormItemGi v-if="model.type == 'course'" span="24 s:12 m:4" class="pr-10px">
              <NSelect
                v-model:value="model.resource_id"
                :options="option?.courses"
                label-field="name"
                value-field="course_id"
                placeholder="请选择课程"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi v-else-if="model.type == 'course_pack'" span="24 s:12 m:4" class="pr-10px">
              <NSelect
                v-model:value="model.resource_id"
                :options="option?.course_packs"
                label-field="name"
                value-field="course_pack_id"
                placeholder="请选择课程包"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi v-else-if="model.type == 'topic'" span="24 s:12 m:4" class="pr-10px">
              <NSelect
                v-model:value="model.resource_id"
                :options="option?.topics"
                label-field="name"
                value-field="topic_id"
                placeholder="请选择题库"
                clearable
              />
            </NFormItemGi>

            <NFormItemGi span="24 s:12 m:3" class="pr-10px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon"/>
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon"/>
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
