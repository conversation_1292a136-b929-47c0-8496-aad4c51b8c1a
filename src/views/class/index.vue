<script setup lang="tsx">
import { ref } from 'vue';
import { NButton, NTag, NSpace, NPopconfirm, NGrid, NGridItem, NTooltip } from 'naive-ui';
import { fetchClassList, removeClass } from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';
import { classStatusRecord, classTypeRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { getUserInfo } from '@/store/modules/auth/shared';
import DateFormat from '@/components/common/date/date-format.vue';
import TableHeaderOperation from './modules/table-header-operation.vue';
import TableOperate from './modules/table-operate.vue';
import TableSearch from './modules/table-search.vue';
import TableQrcode from "./modules/table-qrcode.vue";

const { routerPushByKey } = useRouterPush();

const userInfo = getUserInfo();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchClassList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    user_id: null,
    name: null,
    phone: null,
    status: null,
    class_id: null,
    type: null,
    resource_id: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48,
    },
    {
      key: 'id',
      title: 'ID',
      align: 'center',
    },
    {
      key: 'name',
      title: '班级信息',
      align: 'left',
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'default',
          1: 'info',
          2: 'success'
        };

        const label = classStatusRecord[row.status];

        return (
          <NGrid y-gap={2}>
            <NGridItem span={24}>
               班级名称：<div class="inline-block class-title" >{row.name}</div>
            </NGridItem>
            <NGridItem span={24}>负责老师：{row.manager ? (row.manager.real_name || row.manager.username) : '-'}</NGridItem>
            <NGridItem span={24}>培训状态：
              <NTag type={tagMap[row.status]} bordered={false} size="small">
                {label}
              </NTag>
            </NGridItem>
          </NGrid>
        );
      }
    },
    {
      key: 'total_enrollments',
      title: '培训信息',
      align: 'left',
      render: row => (
        <NGrid y-gap={2}>
          <NGridItem span={24}>培训科目：{toTypeLabel(row)}</NGridItem>
          <NGridItem span={24}>培训人数：{row.total_enrollments}</NGridItem>
          <NGridItem span={24}>完成学时人数：{row.type == 'course' ? row.total_course_finished : '-'}</NGridItem>
        </NGrid>
      )
    },
    {
      key: 'exam_enabled',
      title: '考试信息',
      align: 'left',
      render: row => {
        const tagType = row.exam_enabled ? 'success' : 'info';

        return (
          <NGrid y-gap={2}>
            <NGridItem span={24}>是否考试：<NTag type={tagType} size="small">{row.exam_enabled ? '是' : '否'}</NTag></NGridItem>
            <NGridItem span={24}>考试人数：{row.exam_enabled ? row.total_examined : '-'}</NGridItem>
            <NGridItem span={24}>及格人数：{row.exam_enabled ? row.total_passed : '-'}</NGridItem>
          </NGrid>
        );
      }
    },
    {
      key: 'created_at',
      title: '日期信息',
      align: 'left',
      render: row => (
        <NGrid y-gap={2}>
          <NGridItem span={24}>计划开始时间：<DateFormat date={row.start_at} format="yyyy-MM-dd" /></NGridItem>
          <NGridItem span={24}>计划结束时间：<DateFormat date={row.end_at} format="yyyy-MM-dd" /></NGridItem>
          <NGridItem span={24}>实际结束时间：<DateFormat date={(row.status == 2 && row.actual_end_at == null) ? row.end_at : row.actual_end_at} format="yyyy-MM-dd" /></NGridItem>
        </NGrid>
      )
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      render: row => (
        <NSpace size="small" justify="center">
          <NButton type="primary" size="small" onClick={() => show(row)}>
            详情
          </NButton>
          <TableQrcode url={`${userInfo.config?.org_pc_url}/${userInfo.org?.sid}/apply/${row.sid}`} />
          <NTooltip trigger="hover" placement="top" disabled={row.status != 2}>
            {{
              default: () => '已结束的班级不可编辑',
              trigger: () => (
                <NButton type="primary" size="small" disabled={row.status == 2} onClick={() => edit(row)}>
                  编辑
                </NButton>
              )
            }}
          </NTooltip>
          <NTooltip trigger="hover" placement="top" disabled={row.total_enrollments == 0 }>
            {{
              default: () => '有学员的班级不可删除',
              trigger: () => (
                <NPopconfirm onPositiveClick={() => remove(row)}>
                  {{
                    default: () => '确定要删除吗？',
                    trigger: () => (
                      <NButton size={'small'} type={'error'} disabled={row.total_enrollments > 0 }>
                        删除
                      </NButton>
                    )
                  }}
                </NPopconfirm>
              )
            }}
          </NTooltip>
        </NSpace>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  checkedRowKeys,
  handleOperate,
  handleAdd,
  onDeleted
} = useTableOperate(data, getData);

const options = ref<Api.Org.ClassOption | null>(null);

function toTypeLabel(row: Api.Org.Class) {
  if (row?.resource) {
    return `（${classTypeRecord[row.type]}）` + row.resource.name;
  } else {
    return '-'
  }
}

function show(row: Api.Org.Class) {
  routerPushByKey('classdetail', { query: { id: row.id.toString() } });
}

function edit(row: Api.Org.Class) {
  handleOperate('edit', row);
}

function add() {
  if (options.value) {
    handleAdd();
  }
}

async function remove(row: Api.Org.Class) {
  const { data } = await removeClass(row.id);
  if (data != null && data.success == true) {
    onDeleted();
  }
}

function handleLoaded(o: Api.Org.ClassOption) {
  options.value = o;
}

function handleRefresh(clear?: boolean) {
  if (clear) {
    checkedRowKeys.value = [];
  }

  getData();
}
</script>

<template>
  <div class="flex-col-stretch gap-16px lt-sm:overflow-auto">
    <TableSearch v-model:model="searchParams" :option="options" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="班级列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :row-keys="checkedRowKeys"
          :loading="loading"
          @add="add"
          @refresh="handleRefresh"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        :scroll-x="962"
        :loading="loading"
        :row-key="row => row.id"
        :pagination="mobilePagination"
        remote
        size="small"
        class="sm:h-full"
      />
      <TableOperate
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
        @loaded="handleLoaded"
      />
    </NCard>
  </div>
</template>

<style>
.class-title {
  font-weight: bold;
}
</style>
