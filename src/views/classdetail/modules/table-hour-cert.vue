<script lang="tsx" setup>
import { ref } from 'vue';
import { useLoadingBar } from "naive-ui";
import { getEnrollmentHourCert } from '@/service/api';

const loadingBar = useLoadingBar();

const showModal = ref<boolean>(false);
const loading = ref<boolean>(false);
const imageUrl = ref<string>('');

async function open(row: Api.Org.Enrollment) {
  if (loading.value) {
    return;
  }

  loadingBar.start();

  loading.value = true;

  const { error, data } = await getEnrollmentHourCert(row.id);

  loading.value = false;

  if (error) {
    loadingBar.error();
    return;
  }

  loadingBar.finish();

  imageUrl.value = data.image_url;
  showModal.value = true;
}

defineExpose({
  open
})
</script>

<template>
  <n-modal v-model:show="showModal">
    <n-card
      style="width: 600px"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <NImage :src="imageUrl" :preview-disabled="true" />
    </n-card>
  </n-modal>
</template>
