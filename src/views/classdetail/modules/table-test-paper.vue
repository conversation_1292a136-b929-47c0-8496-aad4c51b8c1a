<script lang="tsx" setup>
import { ref } from 'vue';
import { useLoadingBar } from 'naive-ui'
import { getEnrollmentTestPaper } from '@/service/api';
import { isEmpty } from "lodash-es";
import { showWaningMessage } from "@/utils/message";

const loadingBar = useLoadingBar();

const visible = ref<boolean>(false);
const loading = ref<boolean>(false);
const testPaper = ref<Api.Org.EnrollmentTestPaper | null>(null);
const enrollmentId = ref<number>(0);

async function open(row: Api.Org.Enrollment) {
  if (loading.value) {
    return;
  }

  loadingBar.start();

  loading.value = true;

  const { error, data } = await getEnrollmentTestPaper(row.id);

  loading.value = false;

  if (error) {
    loadingBar.error();
    return;
  }

  if (isEmpty(data)) {
    loadingBar.error();
    showWaningMessage('无考试信息');
    return;
  }

  loadingBar.finish();

  enrollmentId.value = row.id;
  testPaper.value = data;
  visible.value = true;
}

defineExpose({
  open
})
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="1080">
    <NDrawerContent title="试卷详情" :native-scrollbar="false" closable>
      <NCard v-if="testPaper" class="shadow-sm">
        <NSpace vertical size="large">
          <NSpace justify="center">
            <NText class="text-26px font-800">考试试卷</NText>
            <ExportButton name="下载试卷" size="medium" export-type="test_paper" :export-ids="[enrollmentId]" />
          </NSpace>

          <NDescriptions bordered size="large" :column="2" label-placement="left">
            <NDescriptionsItem label="学号（ID）">
              <NText>{{ testPaper.student_id }}</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="姓名">
              <NText>{{ testPaper.name }}</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="联系电话">
              <NText>{{ testPaper.phone }}</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="身份证号">
              <NText>{{ testPaper.id_card }}</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="工作单位">
              <NText>{{ testPaper.company }}</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="考试课程">
              <NText>{{ testPaper.test_name }}</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="考试分数">
              <NTag :type="testPaper.passed == '及格' ? 'success' : 'warning'" size="medium" round>{{ testPaper.score }}</NTag>
            </NDescriptionsItem>
            <NDescriptionsItem label="是否及格">
              <NTag :type="testPaper.passed == '及格' ? 'success' : 'warning'" size="medium" round>{{ testPaper.passed }}</NTag>
            </NDescriptionsItem>
            <NDescriptionsItem label="考试时间">
              <NText>{{ testPaper.test_time }}</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="考试用时">
              <NText>{{ testPaper.duration }}</NText>
            </NDescriptionsItem>
          </NDescriptions>

          <NSpace justify="center">
            <NText class="text-24px font-500">考试试题</NText>
          </NSpace>

          <NTable :bordered="true" :single-line="false" class="overflow-hidden rounded-md shadow-sm">
            <thead>
            <tr>
              <th class="w-70% bg-blue-50 p-3 text-left">题目</th>
              <th class="bg-blue-50 p-3 text-center">考生答案</th>
              <th class="bg-blue-50 p-3 text-center">正确答案</th>
              <th class="bg-blue-50 p-3 text-center">是否正确</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(question, index) in testPaper.questions" :key="index">
              <td>
                <NSpace vertical size="small">
                  <div class="border-b-1 pb-3">
                    <NText class="font-bold">{{ index + 1 }}、{{ question.content }}</NText>
                  </div>

                  <NSpace vertical size="small" class="mt-2">
                    <NText v-for="(option, optIndex) in question.options" :key="optIndex">
                      {{ option }}
                    </NText>
                  </NSpace>
                </NSpace>
              </td>
              <td class="text-center align-middle">
                <NTag type="warning" size="large" round>{{ question.student_answer }}</NTag>
              </td>
              <td class="text-center align-middle">
                <NTag type="success" size="large" round>{{ question.correct_answer }}</NTag>
              </td>
              <td class="text-center align-middle">
                <NText :type="question.student_answer == question.correct_answer ? 'success' : 'error'" class="font-bold">
                  {{ question.is_correct }}
                </NText>
              </td>
            </tr>
            </tbody>
          </NTable>
        </NSpace>
      </NCard>
    </NDrawerContent>
  </NDrawer>
</template>
