<script setup lang="tsx">
interface Props {
  disabled?: boolean;
}

defineProps<Props>();

interface Emits {
  (e: 'refresh', clear?: boolean): void;
}

const emit = defineEmits<Emits>();

const rowKeys = defineModel<number[]>('rowKeys', {
  default: () => []
});

function refresh(clear?: boolean) {
  emit('refresh', clear);
}
</script>

<template>
  <NSpace>
    <ExportButton name="下载学时证明" export-type="hour_cert" :export-ids="rowKeys" :disabled="disabled" @refresh="refresh(true)" />
    <ExportButton name="下载学习记录" export-type="hour_record" :export-ids="rowKeys" :disabled="disabled" @refresh="refresh(true)" />
    <ExportButton name="下载考试试卷" export-type="test_paper" :export-ids="rowKeys" :disabled="disabled" @refresh="refresh(true)" />
    <ExportButton name="下载报名表" export-type="org_enrollment_form" :export-ids="rowKeys" :disabled="disabled" @refresh="refresh(true)" />
    <ExportButton name="一键打包下载" export-type="org_download_pack" :export-ids="rowKeys" :disabled="disabled" @refresh="refresh(true)" />
  </NSpace>
</template>

<style scoped></style>
