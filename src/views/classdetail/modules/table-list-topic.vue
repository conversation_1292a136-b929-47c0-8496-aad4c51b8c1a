<script setup lang="tsx">
import { onMounted } from 'vue';
import { NTag, NSpace } from 'naive-ui';
import { fetchEnrollmentList } from '@/service/api';
import { enrollmentStatusRecord } from '@/constants/business';
import { useTable } from '@/hooks/common/table';
import DateFormat from '@/components/common/date/date-format.vue';
import TableSearch from './table-search.vue';

interface Props {
  detail: Api.Org.Class;
}

const props = defineProps<Props>();

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  updateSearchParams
} = useTable({
  immediate: false,
  apiFn: fetchEnrollmentList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 20,
    id: null,
    name: null,
    phone: null,
    status: null,
    class_id: null
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      align: 'center',
    },
    {
      key: 'student.name' as any,
      title: '姓名',
      align: 'center',
      render: row => {
        return row.student?.name ?? '-';
      }
    },
    {
      key: 'student.phone' as any,
      title: '手机号码',
      align: 'center',
      render: row => {
        return row.student?.phone ?? '-';
      }
    },
    {
      key: 'student.id_card_number' as any,
      title: '身份证号码',
      align: 'center',
      render: row => {
        return row.student?.id_card_number ?? '-';
      }
    },
    {
      key: 'status',
      title: '完成情况',
      align: 'center',
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'default',
          1: 'info',
          2: 'success',
          3: 'error',
        };

        const label = enrollmentStatusRecord[row.status];

        return (
          <NTag type={tagMap[row.status]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    }
  ]
});

function handleReset() {
  updateSearchParams({
    class_id: props.detail.id,
    id: null,
    name: null,
    phone: null,
    status: null,
  });

  getData();
}

onMounted( () => {
  updateSearchParams({
    class_id: props.detail.id,
  });

  getData();
})
</script>

<template>
  <div class="min-h-500px">
    <NCard title="学员列表" size="small" class="sm:flex-1-hidden card-wrapper">
      <NSpace vertical>
        <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getDataByPage" />

        <NDataTable
          :columns="columns"
          :data="data"
          :loading="loading"
          :row-key="row => row.id"
          :pagination="mobilePagination"
          remote
          size="small"
        />
      </NSpace>
    </NCard>
  </div>
</template>

<style scoped></style>
