<script setup lang="tsx">
import { $t } from '@/locales';
import { enrollmentStatusOptions } from '@/constants/business';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

type SearchParams = {
  id?: string;
  name?: string;
  phone?: string;
  status?: number;
}

const emit = defineEmits<Emits>();

const model = defineModel<SearchParams>('model', { required: true });

async function reset() {
  emit('reset');
}

async function search() {
  emit('search');
}
</script>

<template>
  <NForm :model="model">
    <NGrid responsive="screen" item-responsive>
      <NFormItemGi span="24 s:12 m:2" label="ID" class="pr-10px">
        <NInput v-model:value="model.id" clearable />
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:2" label="姓名" class="pr-10px">
        <NInput v-model:value="model.name" clearable />
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:2" label="手机号码" class="pr-10px">
        <NInput v-model:value="model.phone" clearable />
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:2" label="状态" class="pr-10px">
        <NSelect v-model:value="model.status" :options="enrollmentStatusOptions" clearable/>
      </NFormItemGi>

      <NFormItemGi span="24 s:12 m:3" class="pr-10px">
        <NSpace class="w-full" justify="end">
          <NButton @click="reset">
            <template #icon>
              <icon-ic-round-refresh class="text-icon" />
            </template>
            {{ $t('common.reset') }}
          </NButton>
          <NButton type="primary" ghost @click="search">
            <template #icon>
              <icon-ic-round-search class="text-icon" />
            </template>
            {{ $t('common.search') }}
          </NButton>
        </NSpace>
      </NFormItemGi>
    </NGrid>
  </NForm>
</template>

<style scoped></style>
