<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { NButton, NTag, NSpace, NDropdown } from 'naive-ui';
import { fetchEnrollmentList, examRetakeEnrollment } from '@/service/api';
import { toHour } from "@/utils/common";
import { enrollmentStatusRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { openWebUrl } from "@/utils/common";
import DateFormat from '@/components/common/date/date-format.vue';
import TableHeaderOperation from './table-header-operation.vue';
import TableSearch from './table-search.vue';
import TableTestPaper from "./table-test-paper.vue";
import TableHourCert from './table-hour-cert.vue';

interface Props {
  detail: Api.Org.Class;
}

const props = defineProps<Props>();

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  updateSearchParams
} = useTable({
  immediate: false,
  apiFn: fetchEnrollmentList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 50,
    id: null,
    name: null,
    phone: null,
    status: null,
    class_id: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48,
    },
    {
      key: 'id',
      title: 'ID',
      align: 'center',
      width: 100,
    },
    {
      key: 'student.name' as any,
      title: '姓名',
      align: 'center',
      width: 100,
      render: row => {
        return row.student?.name ?? '-';
      }
    },
    {
      key: 'student.phone' as any,
      title: '手机号码',
      align: 'center',
      minWidth: 120,
      render: row => {
        return row.student?.phone ?? '-';
      }
    },
    {
      key: 'student.id_card_number' as any,
      title: '身份证号码',
      align: 'center',
      minWidth: 200,
      render: row => {
        return row.student?.id_card_number ?? '-';
      }
    },
    {
      key: 'learned_duration',
      title: '已修学时',
      align: 'center',
      width: 80,
      render: row => {
        return row.hour as number;
      }
    },
    {
      key: 'exam_taken',
      title: '是否考试',
      align: 'center',
      width: 100,
      render: row => {
        if (row.exam_taken) {
          return <NTag type="success">是</NTag>;
        } else {
          return <NTag type="info">否</NTag>;
        }
      }
    },
    {
      key: 'exam_score',
      title: '考试成绩',
      align: 'center',
      width: 100,
      render: row => {
        return row.exam_taken ? row.exam_score : '-';
      }
    },
    {
      key: 'exam_count',
      title: '考试次数',
      align: 'center',
      width: 100,
      render: row => {
        return row.exam_taken ? row.exam_count : '-';
      }
    },
    {
      key: 'exam_passed',
      title: '是否及格',
      align: 'center',
      width: 100,
      render: row => {
        if (row.exam_taken) {
          if (row.exam_passed) {
            return <NTag type="success">是</NTag>;
          } else {
            return <NTag type="info">否</NTag>;
          }
        } else {
          return '-';
        }
      }
    },
    {
      key: 'status',
      title: '完成情况',
      align: 'center',
      width: 100,
      render: row => {
        const tagMap: Record<number, NaiveUI.ThemeColor> = {
          0: 'default',
          1: 'info',
          2: 'success',
          3: 'warning',
          4: 'error',
        };

        const label = enrollmentStatusRecord[row.status];

        return (
          <NTag type={tagMap[row.status]} bordered={false}>
            {label}
          </NTag>
        );
      }
    },
    {
      key: 'created_at',
      title: '创建时间',
      align: 'center',
      width: 160,
      render: row => {
        return <DateFormat date={row.created_at} />;
      }
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 160,
      render: row => (
        <NDropdown trigger="click" options={getOptions(row)} onSelect={(key: string) => handleSelect(key, row)}>
          <NButton type="primary">
            更多操作
          </NButton>
        </NDropdown>
      )
    }
  ]
});

const { checkedRowKeys } = useTableOperate(data, getData);

const examRef = ref();
const certRef = ref();

function getOptions(row: Api.Org.Enrollment) {
  const options = [
    {
      label: '学时证明',
      key: 'hour_cert'
    },
    {
      label: '学习记录',
      key: 'hour_record'
    }
  ];

  if (row.exam_taken) {
    options.push({
      label: '考试试卷',
      key: 'exam_paper'
    });
  }

  if (
    row.exam_taken && !row.exam_passed && row.exam_retake != 1 &&
    row.classroom && row.classroom.exam_limit && row.exam_count >= row.classroom.exam_limit_count
  ) {
    options.push({
      label: '重新考试',
      key: 'exam_retake'
    });
  }

  return options;
}

function handleSelect(key: string, row: Api.Org.Enrollment) {
  switch (key) {
    case 'hour_cert':
      // openWebUrl(row?.hour_cert_url)
      certRef.value.open(row);
      break;
    case 'hour_record':
      openWebUrl(row?.study_record_url)
      break;
    case 'exam_paper':
      examRef.value.open(row);
      break;
    case 'exam_retake':
      handleExamRetake(row.id);
      break;
  }
}

async function handleExamRetake(id: number) {
  const { error } = await examRetakeEnrollment(id);

  if (error) {
    return;
  }

  await getData();
}

function handleReset() {
  updateSearchParams({
    class_id: props.detail.id,
    id: null,
    name: null,
    phone: null,
    status: null,
  });

  getData();
}

function handleRefresh(clear?: boolean) {
  if (clear) {
    checkedRowKeys.value = [];
  }

  getData();
}

onMounted( () => {
  updateSearchParams({
    class_id: props.detail.id,
  });

  getData();
})
</script>

<template>
  <div class="min-h-500px">
    <NCard title="学员列表" size="small" class="sm:flex-1-hidden card-wrapper">
      <NSpace vertical>
        <TableSearch v-model:model="searchParams" @reset="handleReset" @search="getDataByPage" />

        <TableHeaderOperation
          v-model:row-keys="checkedRowKeys"
          :disabled="checkedRowKeys.length === 0"
          @refresh="handleRefresh"
        />

        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="data"
          :loading="loading"
          :row-key="row => row.id"
          :pagination="mobilePagination"
          remote
          size="small"
        />
      </NSpace>

      <TableTestPaper ref="examRef" />
      <TableHourCert ref="certRef" />
    </NCard>
  </div>
</template>

<style scoped></style>
