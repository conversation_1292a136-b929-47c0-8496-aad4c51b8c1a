<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { finishedClass, getClass } from '@/service/api';
import { pageFail } from '@/utils/tab';
import { classStatusRecord, classTypeRecord } from '@/constants/business'
import TableListCourse from './modules/table-list-course.vue';
import TableListTopic from './modules/table-list-topic.vue';

const route = useRoute();

const detail = ref<Api.Org.Class | null>(null);
const loading = ref<boolean>(false);

const tagMap: Record<number, NaiveUI.ThemeColor> = {
  0: 'default',
  1: 'info',
  2: 'success'
};

function toRate(count: number, total: number) {
  if (total > 0) {
    return parseFloat(((count / total) * 100).toFixed(2));
  }
  return 0;
}

async function finished() {
  loading.value = true;

  const id: number = detail.value?.id as number;

  const { error } = await finishedClass(id);

  loading.value = false;

  if (error) {
    return;
  }

  detail.value = null;

  await init(id);

  window.$message?.success('操作成功');
}

async function init(id: number) {
  const { error, data } = await getClass(id);

  if (error) {
    await pageFail();
    return;
  }

  detail.value = data;
}

onMounted(async () => {
  if (!route.query?.id) {
    await pageFail();
    return;
  }

  await init(parseInt(route.query.id as string));
})
</script>

<template>
  <div class="gap-16px">
    <NCard v-if="detail" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <div class="flex flex-row detail-box">
          <div class="text-20px font-bold">班级详情</div>
          <div class="pl-1">
            <NTag :type="tagMap[detail.status]">{{ classStatusRecord[detail.status] }}</NTag>
          </div>
          <div class="pl-10">
            <NSpace>
              <NButton v-if="detail.status == 1" type="success" :disabled="loading" @click="finished">学期结束</NButton>
              <NTooltip :placement="top" :disabled="detail.status == 2">
                <template #trigger>
                  <ExportButton :disabled="detail.status != 2" v-if="detail.type == 'course'" name="下载一期一档" export-type="student_archive" :export-ids="[detail.id]" />
                </template>
                已结束的班级才可以下载
              </NTooltip>
            </NSpace>
          </div>
        </div>
      </template>

      <NSpace vertical>
        <NCard title="班级信息" size="small" >
          <div class="class-list">
            <div class="class-list-item">
              <div class="class-info-title">班级名称</div>
              <div class="class-info-desc">{{ detail.name }}</div>
            </div>
            <div class="class-list-item">
              <div class="class-info-title">{{ classTypeRecord[detail.type] }}</div>
              <div class="class-info-desc class-info-course">{{ detail.resource.name }}</div>
            </div>
            <div class="class-list-item">
              <div class="class-info-title">负责老师</div>
              <div class="class-info-desc">{{ detail.manager?.real_name }}</div>
            </div>
            <div class="class-list-item">
              <div class="class-info-title">参培人数</div>
              <div class="class-info-desc">{{ detail.total_enrollments }}</div>
            </div>
            <div class="class-list-item">
              <div class="class-info-title">应修学时</div>
              <div class="class-info-desc">{{ detail.resource?.hour }}</div>
            </div>
            <div class="class-list-item">
              <div class="class-info-title">培训时间</div>
              <div class="class-info-desc">
                <div v-if="detail.start_at">
                  <DateFormat :date="detail.start_at" format="yyyy年MM月dd日" />
                  -
                  <DateFormat :date="detail.end_at" format="yyyy年MM月dd日" />
                </div>
                <div v-else>-</div>
              </div>
            </div>
          </div>
        </NCard>

        <NCard v-if="detail.type == 'course'" title="学习统计" size="small" >
          <div class="class-list">
            <div class="class-list-item">
              <div class="class-stats-count">{{ detail.total_course_finished }}</div>
              <div class="class-stats-desc">完成应修学时人数</div>
            </div>
            <div class="class-list-item">
              <div class="class-stats-count">{{ toRate(detail.total_course_finished, detail.total_enrollments) }}%</div>
              <div class="class-stats-desc">应修学时完成率</div>
            </div>
            <div class="class-list-item">
              <div class="class-stats-count">{{ detail.total_examined }}</div>
              <div class="class-stats-desc">考试人数</div>
            </div>
            <div class="class-list-item">
              <div class="class-stats-count">{{ detail.total_passed }}</div>
              <div class="class-stats-desc">考试通过人数</div>
            </div>
            <div class="class-list-item">
              <div class="class-stats-count">{{ toRate(detail.total_passed, detail.total_examined) }}%</div>
              <div class="class-stats-desc">考试及格率</div>
            </div>
          </div>
        </NCard>

        <TableListTopic v-if="detail.type == 'topic'" :detail="detail" />
        <TableListCourse v-else :detail="detail" />
      </NSpace>
    </NCard>
  </div>
</template>

<style scoped>
.detail-box {
  display: flex;
  flex-direction: row;
  line-height: 48px;
}

.class-list {
  display: flex;
  flex-direction: row;
}

.class-list-item {
  display: flex;
  flex-direction: column;
  padding-right: 52px;
  padding-top: 6px;
  min-width: 150px;
  line-height: 28px;
}

.class-info-title {
  font-size: 14px;
  font-weight: bold;
}

.class-info-desc {
  font-size: 18px;
  font-weight: bold;
}

.class-info-course {
  max-width: 320px;
}

.class-stats-count {
  font-size: 20px;
  font-weight: bold;
}

.class-stats-desc {
  font-size: 14px;
  font-weight: bold;
}
</style>
