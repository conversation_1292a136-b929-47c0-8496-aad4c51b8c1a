import { transformRecordToOption } from '@/utils/common';

export const enableStatusRecord: Record<number, string> = {
  0: '禁用',
  1: '启用'
};

export const enableStatusOptions = transformRecordToOption(enableStatusRecord);

export const displayRecord: Record<number, string> = {
  0: '隐藏',
  1: '显示'
};

export const displayOptions = transformRecordToOption(displayRecord);

export const classTypeRecord: Record<Api.Org.ClassType, string> = {
  course: '课程',
  course_pack: '课程包',
  topic: '题库',
};

export const classTypeOptions = transformRecordToOption(classTypeRecord);

export const classStatusRecord: Record<number, string> = {
  0: '待开始',
  1: '培训中',
  2: '已结束'
};

export const classStatusOptions = transformRecordToOption(classStatusRecord);

export const enrollmentStatusRecord: Record<number, string> = {
  0: '待开课',
  1: '学习中',
  2: '已完成',
  3: '已过期',
  4: '未完成',
};

export const enrollmentStatusOptions = transformRecordToOption(enrollmentStatusRecord);

export const enrollRecordStatusRecord: Record<number, string> = {
  0: '待审核',
  1: '审核通过',
  2: '审核拒绝',
};

export const enrollRecordStatusOptions = transformRecordToOption(enrollRecordStatusRecord);


export const formFieldRecord: Record<Api.Org.EnrollmentFormFieldType, string> = {
  'text': '单行文本',
  'textarea': '多行文本',
  'radio': '单选框',
  'checkbox': '多选框',
  'select': '下拉选择',
  'cascade': '联动选择',
  'region': '地区选择',
  'date': '日期选择',
  'pic': '上传图片',
  'file': '上传文件',
  'sign': '签名',
  'photo': '形象照',
  'work_unit': '工作单位',
};

export const formFieldOptions = transformRecordToOption(formFieldRecord);


export const templateTypeRecord: Record<Api.Org.TemplateType, string> = {
  'hour_cert': '学时证明',
  'student_archive': '一期一档',
  'hour_record': '学习记录',
  'org_enrollment_form': '报名表',
};

export const templateTypeOptions = transformRecordToOption(templateTypeRecord);

export const exportStatusRecord: Record<number, string> = {
  0: '进行中',
  1: '导出完成',
  2: '导出失败',
  3: '部分完成',
};

export const exportStatusOptions = transformRecordToOption(exportStatusRecord);


export const exportTypeRecord: Record<Api.Common.ExportType, string> = {
  'hour_cert': '学时证明',
  'student_archive': '一期一档',
  'hour_record': '学习记录',
  'test_paper': '考试试卷',
  'org_enrollment': '机构学员',
};

export const exportTypeOptions = transformRecordToOption(exportTypeRecord);

export const balanceRecordTypeRecord: Record<Api.Org.BalanceRecordType, string> = {
  income: '充值',
  expense: '消费',
  refund: '退款'
};

export const balanceRecordTypeOptions = transformRecordToOption(balanceRecordTypeRecord);

export const examConfigTypeRecord: Record<Api.Train.TopicExamConfigType, string> = {
  single_choice: '单选题',
  multiple_choice: '多选题',
  judge: '判断题'
};

export const examConfigTypeOptions = transformRecordToOption(examConfigTypeRecord);

// 订单状态
export const orderStatusRecord: Record<number, string> = {
  0: '待支付',
  1: '已支付',
  2: '已退款'
};

export const orderStatusOptions = transformRecordToOption(orderStatusRecord);

// 支付渠道
export const paymentChannelRecord: Record<number, string> = {
  1: '线上支付',
  2: '线下支付'
};

export const paymentChannelOptions = transformRecordToOption(paymentChannelRecord);

// 业务类型
export const businessTypeRecord: Record<string, string> = {
  course: '课程',
  "cms.course": '课程',
  course_pack: '课程包',
  topic: '题库',
};

export const businessTypeOptions = transformRecordToOption(businessTypeRecord);

