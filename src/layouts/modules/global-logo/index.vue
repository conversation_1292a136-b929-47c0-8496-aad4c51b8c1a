<script setup lang="ts">
import { getUserInfo } from '@/store/modules/auth/shared';

defineOptions({
  name: 'GlobalLogo'
});

interface Props {
  /** Whether to show the title */
  showTitle?: boolean;
}

withDefaults(defineProps<Props>(), {
  showTitle: true
});

const userInfo = getUserInfo();
</script>

<template>
  <RouterLink to="/" class="w-full flex-center nowrap-hidden">
    <SystemLogo />
    <h2 v-if="userInfo.org?.alias" class="pl-8px text-16px text-primary font-bold transition duration-300 ease-in-out org-name">
      {{ userInfo.org.alias }}
    </h2>
    <n-ellipsis v-else class="w-140px text-16px text-primary font-bold transition duration-300 ease-in-out">
      {{ userInfo.org?.name }}
    </n-ellipsis>
  </RouterLink>
</template>

<style scoped>
  .org-name {
    width: 140px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
