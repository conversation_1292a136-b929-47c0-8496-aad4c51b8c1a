import dayjs from 'dayjs';

/**
 * 日期转时间戳
 *
 * @param date 日期
 * @returns
 */
export function dateToUnix(date: string): number {
  return dayjs(date).unix();
}

/**
 * 获取对应天数的时间戳
 *
 * @param days 天数
 * @param type 类型
 * @returns
 */
export function dateSubtractToUnix(days: number = 1, type: 'start' | 'end' = 'start'): number {
  let date = dayjs().subtract(days, 'day').format('YYYY-MM-DD');

  if (type === 'end') {
    date = `${date} 23:59:59`;
  } else {
    date = `${date} 00:00:00`;
  }

  return dayjs(date).unix();
}

/**
 * 获取对应天数的日期时间
 *
 * @param days 天数
 * @param type 类型
 * @returns
 */
export function dateSubtractToDateTime(days: number = 1, type: 'start' | 'end' = 'start'): string {
  return dateFormat(dateSubtractToUnix(days, type));
}

/**
 * 日期格式化
 *
 * @param value 日期|时间戳
 * @param format 格式
 * @returns
 */
export function dateFormat(value: string | number, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  let time: number = 0;

  if (typeof value === 'string') {
    time = dayjs(value).unix() * 1000;
  } else {
    time = value * 1000;
  }

  return dayjs(time).format(format);
}

/**
 * 当前时间
 *
 * @returns string
 */
export function now(): string {
  return dayjs().format('YYYY-MM-DD HH:mm:ss');
}
