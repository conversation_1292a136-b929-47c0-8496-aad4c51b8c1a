import { $t } from '@/locales';
import { v4 as uuidV4 } from 'uuid';
import { useClipboard } from "@vueuse/core";
import { showSuccessMessage, showWaningMessage } from "@/utils/message";

/**
 * Transform record to option
 *
 * @example
 *   ```ts
 *   const record = {
 *     key1: 'label1',
 *     key2: 'label2'
 *   };
 *   const options = transformRecordToOption(record);
 *   // [
 *   //   { value: 'key1', label: 'label1' },
 *   //   { value: 'key2', label: 'label2' }
 *   // ]
 *   ```;
 *
 * @param record
 */
export function transformRecordToOption<T extends Record<string, string>>(record: T) {
  return Object.entries(record).map(([value, label]) => ({
    value,
    label
  })) as CommonType.Option<keyof T>[];
}

/**
 * Translate options
 *
 * @param options
 */
export function translateOptions(options: CommonType.Option<string>[]) {
  return options.map(option => ({
    ...option,
    label: $t(option.label as App.I18n.I18nKey)
  }));
}

/**
 * Toggle html class
 *
 * @param className
 */
export function toggleHtmlClass(className: string) {
  function add() {
    document.documentElement.classList.add(className);
  }

  function remove() {
    document.documentElement.classList.remove(className);
  }

  return {
    add,
    remove
  };
}

/**
 * 获取文件大小
 *
 * @param bytes
 * @returns
 */
export function getFileSize(bytes: number): string {
  const fileSizeUnits = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const base = 1024;
  if (bytes) {
    let i = 0;
    if (bytes >= base) {
      let a = bytes;
      while (true) {
        a /= base;
        i++;
        if (a < base) break;
      }
    }
    return (bytes / Math.pow(base, i)).toFixed(2) + ' ' + fileSizeUnits[i];
  } else {
    return '0 KB';
  }
}

/** 打开WEB地址 */
export function openWebUrl(url?: string) {
  if (!url) {
    return;
  }

  window.open(url, '_blank', 'noopener=yes,noreferrer=yes');
}


export function uuid() {
  return uuidV4().replaceAll('-', '');
}

export function toHour(second: number) {
  return second > 0 ? (second / 3600).toFixed(2) : '0';
}

export async function clipboard(text: string) {
  const { copy, isSupported } = useClipboard();

  if (!isSupported) {
    showWaningMessage('您的浏览器不支持Clipboard API');
    return;
  }

  if (!text) {
    showWaningMessage('请输入要复制的内容');
    return;
  }

  await copy(text);

  showSuccessMessage(`复制成功`);
}
