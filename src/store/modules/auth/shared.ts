import { localStg } from '@/utils/storage';


/** Get token */
export function getToken() {
  return localStg.get('token') || '';
}

export function getUserInfo(): Api.Auth.UserInfo {
  const emptyInfo: Api.Auth.UserInfo = {
    userId: '',
    userName: '',
    roles: [],
    org: null,
    config: null
  };
  return localStg.get('userInfo') || emptyInfo;
}

export function getRoles() {
  const userInfo = getUserInfo();

  return userInfo.roles;
}

/** Clear auth storage */
export function clearAuthStorage() {
  localStg.remove('token');
  localStg.remove('refreshToken');
  localStg.remove('userInfo');
}
