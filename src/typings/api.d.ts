/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    type CommonRecord<T = any> = {
      id: number;
      created_at: string;
      updated_at: string;
      deleted_at?: string;
      status: number;
    } & T;

    type ExportType = 'hour_cert' | 'student_archive' | 'hour_record' | 'test_paper' | 'org_enrollment' | 'org_enrollment_form' | 'org_download_pack';
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string;
      refreshToken: string;
    }

    interface UserInfo {
      userId: string;
      userName: string;
      roles: string[];
      org: Api.Org.Org | null;
      config: {
        org_pc_url: string;
        org_applet_path: string;
        base_url: string;
      } | null;
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  namespace Manage {
    type Role = {
      id: number;
      name: string;
      code: string;
      desc: string;
      created_at: string;
      updated_at: string;
    };

    type Admin = Common.CommonRecord<{
      org_id: number;
      username: string;
      real_name: string;
      phone: string;
      email: string;
      is_main: number;
      last_logged_at: string;
      last_logged_ip: string;
      last_active_at: string;
      last_active_ip: string;
      roles?: AdminRole[];
    }>;

    type AdminRole = {
      id: number;
      admin_id: number;
      role_id: number;
      created_at: string;
      role?: Role;
    };
  }

  namespace System {
    type UploadStorage = 'pub' | 'priv' | 'local';

    type UploadConfig = {
      max_size_kb: number;
      method: string;
      name: string;
      url: string;
      form_params: {
        token: string;
      };
      allow_mime_types: string | string[];
    };

    type UploaderFile = {
      filename: string;
      key: string;
      mime: string;
      size: number;
      url: string;
      created_at?: string;
    };
  }

  namespace Stat {
    type StatOrg = {
      org_id: number;
      enrollments: number;
      classes: number;
      exams: number;
      trained: number;
    }

    interface SubtotalOverview {
      subtotal: StatOrg;
      today: StatOrg;
      yesterday: StatOrg;
    }

    interface DailyOverview extends StatOrg {
      id: number;
      date: string;
      created_at: string;
      updated_at: string;
    }
  }

  namespace Cms {
    type Content = {
      id: number;
      sid: string;
      category_id: number;
      title: string;
      type: number;
      cover: string;
      cover_src: string;
      intro: string;
      view_limit: number;
      charge_credit: number;
      charge_amount: string;
      status_desc: string;
      views: number;
      views_add: number;
      source: string;
      release_at: string;
      recommend_at: string;
      admin_id: number;
      created_at: string;
      updated_at: string;
      deleted_at: string;
    };

    type ContentVideo = {
      content_id: number;
      filepath: string;
      filepath_src: string;
      filesize: string;
      duration: number;
      extend: any;
      created_at: string;
      updated_at: string;
    };

    type ContentCourse = {
      content_id: number;
      topic_id: number;
      teacher_name: string;
      learning_count: number;
      learning_count_add: number;
      sections_count: number;
      try_view_count: number;
      created_at: string;
      updated_at: string;
      buy_course_users?: number;
      chapters?: ContentCourseChapter[];
      content?: Content;
      topic?: Train.Topic;
    };

    type ContentCourseChapter = {
      id: number;
      content_id: number;
      name: string;
      sections_count: number;
      sort: number;
      created_at: string;
      updated_at: string;
      sections?: ContentCourseSection[];
    };

    type ContentCourseSection = {
      id: number;
      content_id: number;
      chapter_id: number;
      ref_video_id: number;
      name: string;
      filepath: string;
      filepath_src: string;
      filesize: number;
      extend: any;
      play_count: number;
      play_complete_count: number;
      duration: number;
      sort: number;
      created_at: string;
      updated_at: string;
      video?: ContentVideo;
    };

    type ChapterSection = {
      id: number;
      course_id: number;
      index: string;
      type: Api.Org.CourseSubType;
      name: string;
      name_desc: string;
      hour: string;
      status: boolean;
      duration: number;
      children: ChapterSection[];
    }

    type ContentCoursePack = {
      content_id: number;
      topic_id: number;
      created_at: string;
      updated_at: string;
      content?: Content;
      topic?: Train.Topic;
    };
  }

  namespace Train {
    type TopicExamConfigType = 'single_choice' | 'multiple_choice' | 'judge';

    type TopicExamConfig = Record<TopicExamConfigType, {
      count: number;
      score: number;
    }>;

    type TopicSubjectTypeCount = Record<TopicExamConfigType, number>;

    type Topic = {
      id: number;
      course_category_id: number;
      course_content_id: number;
      name: string;
      amount: string;
      exam_time: number;
      pass_score: number;
      exam_config: Api.Train.TopicExamConfig | null;
      next_exam_at: string;
      sort: number;
      subjects_count?: number;
      created_at: string;
      updated_at: string;
    };
  }

  namespace Org {
    type Org = {
      id: number;
      sid: string;
      name: string;
      alias: string;
      domain: string;
      logo: string;
      logo_url: string;
      verified_status: 'pending' | 'verified' | 'rejected';
      total_students: number;
      total_classes: number;
      total_trained: number;
      balance: number;
      business_license: string;
      business_license_url: string;
      business_scope?: string;
      contact: string;
      service_qrcode: string;
      service_qrcode_url: string;
      official_seal_image: string;
      official_seal_image_url: string;
      enable_enroll: boolean;
      enroll_background_image: string;
      enroll_background_image_url: string;
      area_code: number;
      area_text: string[];
      verified_at: string;
      created_at: string;
      updated_at: string;
    };

    type OptionCourse = {
      id: number;
      course_id: number;
      name: string;
      price_original: string;
      price_sell: string;
      topic?: Api.Train.Topic;
    };

    type OptionCoursePack = {
      id: number;
      course_pack_id: number;
      name: string;
      price_original: string;
      price_sell: string;
      topic?: Api.Train.Topic;
    };

    type OptionTopic = {
      id: number;
      topic_id: number;
      name: string;
      price_original: string;
      price_sell: string;
    };

    type ClassOption = {
      teacher: boolean;
      manager_id: number;
      managers: Api.Manage.Admin[];
      courses: OptionCourse[];
      course_packs: OptionCoursePack[];
      topics: OptionTopic[];
      template_hours: Template[];
      template_archives: Template[];
    }

    type ClassType = 'course' | 'course_pack' | 'topic';

    type ExamCondition = 'anytime' | 'after_course' | 'fixed_time';
    type ExamMode = 'all' | 'mobile_only' | 'pc_only';

    type Class =  Common.CommonRecord<{
      org_id: number;
      sid: string;
      name: string;
      manager_id: number;
      type: ClassType;
      resource_id: number;
      hour: number;
      status: number;
      total_enrollments: number;
      total_course_finished: number;
      total_examined: number;
      total_passed: number;
      exam_enabled: boolean;
      exam_limit: boolean;
      exam_limit_count: number;
      exam_condition: ExamCondition;
      exam_mode: ExamMode;
      exam_at?: string;
      face_capture_enabled: boolean;
      face_capture_count: number;
      template_custom: boolean;
      template_hour_id: number;
      template_archive_id: number;
      start_at?: string;
      end_at?: string;
      actual_end_at?: string;
      manager?: Api.Manage.Admin;
      resource?: any;
    }>;

    type BalanceRecordType = 'expense' | 'income' | 'refund';

    type BalanceRecord = Common.CommonRecord<{
      org_id: number;
      type: BalanceRecordType;
      origin_balance: number;
      amount: number;
      enroll_id: number;
      ref_id: number;
      status: number;
      remark: string;
    }>;

    type EnrollmentOption = {
      classes: Class[];
      classes_padding: Class[];
      courses: OptionCourse[];
      course_packs: OptionCoursePack[];
      topics: OptionTopic[];
    }

    type Student = Common.CommonRecord<{
      org_id: number;
      user_id: number;
      name: string;
      phone: string;
      work_unit?: string;
      id_card_number: string;
      id_card_front: string;
      id_card_front_url?: string;
      id_card_back: string;
      id_card_back_url?: string;
      photo: string;
      photo_url?: string;
      extra: any;
      latest_start_at: string;
      latest_enroll_at: string;
      enrollments?: Enrollment[];
    }>;

    type Enrollment = Common.CommonRecord<{
      org_id: number;
      user_id: number;
      student_id: number;
      class_id: number;
      hour: number;
      type: ClassType;
      resource_id: number;
      course_id: number;
      started_at: string;
      expired_at: string;
      status: number;
      learned_duration: number;
      exam_taken: boolean;
      exam_passed: boolean;
      exam_score: number;
      exam_retake: number;
      exam_latest_id: number;
      exam_count: number;
      hour_cert_url?: string;
      study_record_url?: string;
      checked?: boolean;
      student?: Student;
      classroom?: Class;
      resource?: any;
      amount?: number;
    }>;

    type EnrollmentTestPaper = {
      student_id: string;
      name: string;
      phone: string;
      id_card: string;
      company: string;
      test_name: string;
      score: string;
      passed: string;
      test_time: string;
      duration: string;
      questions: {
        content: string;
        correct_answer: string;
        student_answer: string;
        is_correct: string;
        options: string[];
      }[];
    };

    type EnrollmentFormFieldType = 'text' | 'textarea' | 'radio' | 'checkbox' | 'select' | 'cascade' | 'date' | 'region' | 'sign' | 'photo' | 'pic' | 'file' | 'work_unit';

    type EnrollmentFormField = {
      id?: string;
      type: EnrollmentFormFieldType;
      name: string;
      required?: boolean;
      placeholder?: string;
      desc?: string;
      config?: {
        min?: number;
        max?: number;
        limit?: number;
        size?: number;
        ext?: string[];
        mask?: string;
        options?: string[];
        cascadeOptions?: {
          label: string;
          value: string;
          children?: {
            label: string;
            value: string;
            children?: {
              label: string;
              value: string;
            }[];
          }[];
        }[];
      };
    };

    type EnrollmentForm = Common.CommonRecord<{
      title: string;
      fields: EnrollmentFormField[];
    }>;

    type EnrollConfig = Common.CommonRecord<{
      org_id: number;
      title: string;
      amount: string;
      sort: number;
    }>;

    type EnrollRecord = Common.CommonRecord<{
      org_id: number;
      user_id: number;
      student_id: number;
      enroll_config_id: number;
      order_id?: number;
      status: number; // 0: 待审核, 1: 审核通过, 2: 审核拒绝
      status_label: string;
      audit_remark?: string;
      audit_at?: string;
      auditor_id?: number;
      auditor?: Api.Manage.Admin;
      enroll_config?: EnrollConfig;
      student?: Student;
    }>;

    type Topic = Common.CommonRecord<{
      org_id: number;
      topic_id: number;
      status: number;
      price_original_30: number;
      price_original_60: number;
      price_sell_30: number;
      price_sell_60: number;
      exam_time: number;
      pass_score: number;
      exam_config: Api.Train.TopicExamConfig | null;
      topic?: Api.Train.Topic;
    }>;

    type Course = Common.CommonRecord<{
      org_id: number;
      course_id: number;
      hour: number;
      status: number;
      price_original: number;
      price_sell: number;
      sort?: number;
      content_course?: Api.Cms.ContentCourse;
    }>;

    type CourseSubType = 'chapter' | 'section';

    type CourseSub = Common.CommonRecord<{
      org_id: number;
      course_id: number;
      type: CourseSubType;
      resource_id: number;
      status: number;
    }>;

    type CoursePack = Common.CommonRecord<{
      org_id: number;
      course_pack_id: number;
      status: number;
      price_original: number;
      price_sell: number;
      content_course_pack?: Api.Cms.ContentCoursePack;
    }>;

    type TemplateType = 'hour_cert' | 'student_archive' | 'hour_record';

    type Template = Common.CommonRecord<{
      org_id: number;
      name: string;
      type: TemplateType;
      is_default: boolean;
      tpl_path: string;
      tpl_path_url?: string;
      format: 'word' | 'excel' | 'pdf';
    }>;

    type ExportType = 'hour_cert' | 'student_archive' | 'hour_record';

    type Export = Common.CommonRecord<{
      org_id: number;
      type: ExportType;
      desc: string;
      admin_id: number;
      status: number;
      file: string;
      error: string;
      admin?: Api.Manage.Admin;
      attachment?: {
        filename: string;
      };
    }>;
    type Download = Common.CommonRecord<{
      url: string;
    }>;

    type LearnCapture = Common.CommonRecord<{
      org_id: number;
      enroll_id: number;
      user_id: number;
      course_id: number;
      section_id: number;
      photo: string;
    }>;

    type Order = Common.CommonRecord<{
      org_id: number;
      user_id: number;
      source_id: number;
      source_table: string;
      business_type: string;
      title: string;
      order_no: string;
      transaction_no: string;
      total_amount: string;
      payment_amount: string;
      payment_channel: number;
      status: number;
      payment_at: string;
      expired_at: string;
      user?: {
        id: number;
        name: string;
        phone: string;
      };
    }>;
  }
}
