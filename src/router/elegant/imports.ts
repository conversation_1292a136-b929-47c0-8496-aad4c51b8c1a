/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  class: () => import("@/views/class/index.vue"),
  classdetail: () => import("@/views/classdetail/index.vue"),
  "content_test-course-pack": () => import("@/views/content/test-course-pack/index.vue"),
  "content_test-course": () => import("@/views/content/test-course/index.vue"),
  "content_test-topic": () => import("@/views/content/test-topic/index.vue"),
  enroll_config: () => import("@/views/enroll/config/index.vue"),
  enroll_enrollment: () => import("@/views/enroll/enrollment/index.vue"),
  "enroll_record-detail": () => import("@/views/enroll/record-detail/index.vue"),
  enroll_record: () => import("@/views/enroll/record/index.vue"),
  enrollment: () => import("@/views/enrollment/index.vue"),
  home: () => import("@/views/home/<USER>"),
  order: () => import("@/views/order/index.vue"),
  student: () => import("@/views/student/index.vue"),
  system_account: () => import("@/views/system/account/index.vue"),
  system_export: () => import("@/views/system/export/index.vue"),
  system_mechanism: () => import("@/views/system/mechanism/index.vue"),
  system_template: () => import("@/views/system/template/index.vue"),
};
