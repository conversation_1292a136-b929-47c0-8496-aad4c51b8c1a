<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue';
import { dateFormat, dateToUnix } from '@/utils/date';

interface Props {
  value?: string | null;
}

interface Emits {
  (e: 'update:value', value: string): void;
  (e: 'change'): void;
}

const emit = defineEmits<Emits>();

const props = withDefaults(defineProps<Props>(), {
  value: ''
});

const timestamp = ref<number | null>(null);

const format = 'YYYY-MM-DD HH:mm:ss';

function onConfirm(value: number) {
  nextTick(() => {
    timestamp.value = value;
    emit('update:value', dateFormat(value / 1000, format));
    emit('change');
  });
}

function onClear() {
  emit('update:value', '');
}

onMounted(() => {
  if (!props.value) {
    return;
  }

  timestamp.value = dateToUnix(props.value) * 1000;
});
</script>

<template>
  <NDatePicker
    v-model:value="timestamp"
    type="datetime"
    format="yyyy-MM-dd HH:mm:ss"
    clearable
    :on-confirm="onConfirm"
    :on-clear="onClear"
  />
</template>

<style scoped></style>
