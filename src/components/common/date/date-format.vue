<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import dayjs from 'dayjs';

interface Props {
  format?: string;
}

withDefaults(defineProps<Props>(), {
  format: 'yyyy-MM-dd HH:mm:ss',
});

const date = defineModel<string | number | null | undefined>('date', {
  default: null
});

const unix = ref(0);

function handelValue() {
  if (date.value) {
    if (typeof date.value === 'number') {
      unix.value = date.value;
    } else {
      unix.value = dayjs(date.value).unix();
    }
  } else {
    unix.value = 0;
  }
}

watch(date, () => {
  handelValue();
})

onMounted(() => {
  handelValue();
});
</script>

<template>
  <view v-if="unix">
    <NTime :time="unix" :format="format" time-zone="Asia/Shanghai" unix />
  </view>
  <view v-else>-</view>
</template>

<style scoped></style>
