<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { dateFormat, dateSubtractToUnix, dateToUnix } from '@/utils/date';

interface Props {
  value: string[] | null;
  days?: number;
  today?: boolean;
  startPlaceholder?: string;
  endPlaceholder?: string;
}

const router = useRoute();

const props = withDefaults(defineProps<Props>(), {
  value: null,
  days: undefined,
  today: true,
  startPlaceholder: '开始时间',
  endPlaceholder: '结束时间'
});

interface Emits {
  (e: 'update:value', value: string[]): void;
  (e: 'change'): void;
}

const emit = defineEmits<Emits>();

const range = ref<[number, number] | null>(null);

const rangeShortcuts = ref({
  今天: () => {
    return [dateSubtractToUnix(0) * 1000, dateSubtractToUnix(0) * 1000] as const;
  },
  昨天: () => {
    return [dateSubtractToUnix(1) * 1000, dateSubtractToUnix(1) * 1000] as const;
  },
  最近一周: () => {
    return [dateSubtractToUnix(7) * 1000, dateSubtractToUnix(1) * 1000] as const;
  },
  最近一个月: () => {
    return [dateSubtractToUnix(30) * 1000, dateSubtractToUnix(1) * 1000] as const;
  },
  最近三个月: () => {
    return [dateSubtractToUnix(90) * 1000, dateSubtractToUnix(1) * 1000] as const;
  }
});

const format = 'YYYY-MM-DD HH:mm:ss';

function onConfirm(value: [number, number]) {
  nextTick(() => {
    range.value = [value[0], value[1] + 86399 * 1000];
    emit('update:value', [dateFormat(range.value[0] / 1000, format), dateFormat(range.value[1] / 1000, format)]);
    emit('change');
  });
}

function onClear() {
  emit('update:value', []);
}

onMounted(() => {
  if (props.value !== null && props.value.length === 2) {
    range.value = [dateToUnix(props.value[0]) * 1000, dateToUnix(props.value[1]) * 1000];
  } else if (router.query.date === 'today') {
    range.value = [dateSubtractToUnix(0) * 1000, dateSubtractToUnix(0, 'end') * 1000];
  } else if (router.query.date === 'yesterday') {
    range.value = [dateSubtractToUnix(1) * 1000, dateSubtractToUnix(1, 'end') * 1000];
  } else if (props.days !== undefined && props.days >= 0) {
    range.value = [dateSubtractToUnix(props.days) * 1000, dateSubtractToUnix(props.today ? 0 : 1, 'end') * 1000];
  }

  if (range.value !== null) {
    emit('update:value', [dateFormat(range.value[0] / 1000, format), dateFormat(range.value[1] / 1000, format)]);
  }
});
</script>

<template>
  <NDatePicker
    v-model:value="range"
    type="datetimerange"
    value-format="yyyy-MM-dd HH:mm:ss"
    clearable
    :start-placeholder="props.startPlaceholder"
    :end-placeholder="props.endPlaceholder"
    :shortcuts="rangeShortcuts"
    :on-confirm="onConfirm"
    :on-clear="onClear"
  />
</template>

<style scoped></style>
