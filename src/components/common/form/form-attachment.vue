<script setup lang="ts">
export type AttachmentFile = {
  type: string;
  type_id: number;
  filename: string;
  filepath: string;
  url: string;
  created_at: string;
};

const attachmentFiles = defineModel<AttachmentFile[]>('value', {
  default: []
});
</script>

<template>
  <NList>
    <NListItem v-for="(item, index) in attachmentFiles" :key="index">
      <NSpace>
        <NText>{{ item.filename }}</NText>
        <DateFormat :date="item.created_at" />
      </NSpace>
    </NListItem>
  </NList>
</template>

<style scoped></style>
