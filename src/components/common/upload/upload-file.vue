<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui';
import { ref, onMounted, nextTick, computed } from 'vue';
import { ArchiveOutline as ArchiveIcon } from '@vicons/ionicons5';
import { getUploadConfig } from '@/service/api';
import { showWaningMessage } from "@/utils/message";

interface Props {
  value?: string;
  preview?: string;
  placeholder?: string;
  tips?: string;
  drag?: boolean;
  fileType?: string[];
  accept?: string[];
  storage?: Api.System.UploadStorage;
  prefix?: number | string;
  maxSize?: number; // Kb
}

interface Emits {
  (e: 'update:value', value: string): void;
  (e: 'successResponse', data?: Api.System.UploaderFile): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {
  value: '',
  preview: '',
  placeholder: '点击或者拖动文件到该区域来上传',
  tips: '请不要上传敏感数据，比如你的银行卡号和密码，信用卡号有效期和安全码',
  drag: false,
  fileType: [],
  storage: 'pub',
  prefix: 0,
  maxSize: 0
});

// 计算 accept 属性值
const acceptTypes = computed(() => {
  if (!props.accept || props.accept.length === 0) {
    return undefined;
  }
  return props.accept.map(type => `.${type}`).join(',');
});

const uploadConfig = ref<Api.System.UploadConfig>();
const previewFileList = ref<UploadFileInfo[]>([]);

function handlePreview(preview: string, filename: string = '') {
  const fileList: UploadFileInfo[] = [];

  if (preview) {
    const id = '1';

    if (!filename) {
      // eslint-disable-next-line no-param-reassign
      filename = preview.split('/').pop() || id;
      // eslint-disable-next-line no-param-reassign
      filename = filename.split('?').shift() || id;
    }

    fileList.push({
      id,
      batchId: id,
      name: filename,
      status: 'finished',
      url: preview
    });
  }

  previewFileList.value = fileList;
}

function handleFinish({ file, event }: { file: UploadFileInfo; event?: ProgressEvent }) {
  const response = JSON.parse((event?.target as XMLHttpRequest).response) as Api.System.UploaderFile;

  handleUpdateValue(response.key);

  nextTick(() => {
    handlePreview(response.url)
  });

  emit('successResponse', response);

  return file;
}

function handleRemove() {
  handleUpdateValue('');

  emit('successResponse');
}

function handleUpdateValue(value: string) {
  emit('update:value', value);
}

async function getConfig() {
  const { data, error } = await getUploadConfig({
    file_types: props.fileType,
    storage: props.storage,
    prefix: props.prefix,
    max_size: props.maxSize
  });
  if (error) {
    showWaningMessage('加载上传配置失败');
    return;
  }

  uploadConfig.value = data;
}

onMounted(() => {
  if (props.preview || props.value) {
    handlePreview(props.preview ? props.preview : props.value);
  }
});

defineExpose({
  handlePreview
});
</script>

<template>
  <NUpload
    v-model:file-list="previewFileList"
    :max="1"
    :multiple="false"
    :action="uploadConfig?.url"
    :name="uploadConfig?.name"
    :method="uploadConfig?.method"
    :data="uploadConfig?.form_params as any"
    :accept="acceptTypes"
    @finish="handleFinish"
    @remove="handleRemove"
    @before-upload="getConfig"
  >
    <NButton v-if="!drag" type="info">上传文件</NButton>
    <NUploadDragger v-else>
      <div style="margin-bottom: 12px">
        <NIcon size="48" :depth="3">
          <archive-icon />
        </NIcon>
      </div>
      <NText style="font-size: 16px">
        {{ placeholder }}
      </NText>
      <NP depth="3" style="margin: 8px 0 0 0">
        {{ tips }}
      </NP>
    </NUploadDragger>
  </NUpload>
</template>

<style scoped></style>
