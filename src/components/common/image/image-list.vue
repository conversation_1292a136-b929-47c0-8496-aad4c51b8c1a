<script setup lang="ts">
interface Props {
  images: string[];
  width?: number;
}

const props = withDefaults(defineProps<Props>(), {
  width: 60
});
</script>

<template>
  <NImageGroup v-if="images.length > 0">
    <NSpace>
      <NImage
        v-for="(item, index) in props.images"
        :key="index"
        :width="props.width"
        :src="item"
        fallback-src="加载失败"
      >
        <template #placeholder>
          <div style="width: 100px; height: 100px; display: flex; align-items: center; justify-content: center; background-color: #0001">Loading</div>
        </template>
      </NImage>
    </NSpace>
  </NImageGroup>
</template>

<style scoped></style>
