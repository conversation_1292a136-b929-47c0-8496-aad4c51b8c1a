<script setup lang="tsx">
import { ref } from 'vue';
import { $t } from '@/locales';
import { debounce } from '@/utils/debounce';

type ApiFn = (id: number, params: object) => Promise<unknown>;

interface Props {
  id: number;
  sort: number;
  updateSort: ApiFn;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'reset'): void;
}

const emit = defineEmits<Emits>();

const sorting = ref<number>(props.sort);

function handleSubmit() {
  debounce(async () => {
    await props.updateSort(props.id, { sort: sorting.value });

    window.$message?.success($t('common.updateSuccess'));

    emit('reset');
  });
}
</script>

<template>
  <NFlex justify="center">
    <NInputNumber v-model:value="sorting" :min="0" :max="10000" class="w-110px" @update:value="handleSubmit" />
  </NFlex>
</template>

<style scoped></style>
