<script setup lang="ts">
import { onMounted, ref } from 'vue';

interface Props {
  text?: string;
  line?: number;
}

const props = withDefaults(defineProps<Props>(), {
  text: '',
  line: 240
});

const tooltip = ref<string>('');

onMounted(() => {
  let split: string[] = [];
  if (props.text) {
    split = props.text.split('');
  }

  let text = '';
  let start = 0;

  split.forEach(item => {
    if (start < 49) {
      text += item;
      start += 1;
    } else {
      text += `${item}<br />`;
      start = 0;
    }
  });

  tooltip.value = text;
});
</script>

<template>
  <NEllipsis :line-clamp="line" expand-trigger="click">
    {{ props.text }}
    <template #tooltip>
      <div style="text-align: center" v-html="tooltip"></div>
    </template>
  </NEllipsis>
</template>
