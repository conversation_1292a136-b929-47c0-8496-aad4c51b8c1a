<script setup lang="ts">
import { ref } from 'vue';
import type { Size, Type } from 'naive-ui/es/button/src/interface';
import { NButton, useDialog } from 'naive-ui';
import { createExport } from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';

interface Props {
  name?: string;
  type?: Type;
  exportType: Api.Common.ExportType;
  exportIds?: number[];
  exportExtra?: Record<string, any>;
  size?: Size;
  text?: boolean;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'small',
  name: '下载当前列表',
  type: 'primary',
  exportIds: undefined,
  exportExtra: undefined,
  text: false,
  disabled: false
});

interface Emits {
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

const { routerPushByKey } = useRouterPush();

const dialog = useDialog();
const loading = ref(false);

async function handleExport() {
  loading.value = true;
  const { error } = await createExport({
    type: props.exportType,
    checked_ids: props.exportIds,
    extra: props.exportExtra
  });
  loading.value = false;
  if (error) {
    return;
  }
  dialog.success({
    title: '导出成功',
    content: '开始导出，是否跳转到[导出记录]页面下载',
    positiveText: '跳转',
    negativeText: '取消',
    onPositiveClick: () => {
      routerPushByKey('system_export');
    }
  });

  emit('refresh');
}
</script>

<template>
  <NButton
    :type="type as Type"
    :text="text"
    :size="size"
    :loading="loading"
    :disabled="loading || disabled"
    @click="handleExport"
  >
    <slot>{{ name }}</slot>
  </NButton>
</template>
