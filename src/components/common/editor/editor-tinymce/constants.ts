export const plugins = [
  'accordion',
  'advlist',
  'anchor',
  'autolink',
  // 'autoresize',
  'autosave',
  'charmap',
  'code',
  'codesample',
  'directionality',
  'emoticons',
  'fullscreen',
  'help',
  'image',
  'importcss',
  'insertdatetime',
  'link',
  'lists',
  'media',
  'nonbreaking',
  'pagebreak',
  'preview',
  'quickbars',
  'save',
  'searchreplace',
  'table',
  // 'template', // template, replaced by Advanced Template
  'visualblocks',
  'visualchars',
  'wordcount'
];

export const toolbar = [
  'undo redo | accordion accordionremove | blocks fontfamily fontsize table link image | bold italic underline strikethrough | align numlist bullist | lineheight outdent indent| forecolor backcolor removeformat | charmap emoticons  | ltr rtl | pagebreak anchor codesample | code fullscreen preview'
];
