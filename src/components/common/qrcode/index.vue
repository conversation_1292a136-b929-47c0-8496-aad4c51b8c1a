<script setup lang="tsx">
import { clipboard } from "@/utils/common";

interface Props {
  value: string;
  copyUrl?: string;
  width?: number;
  level?: 'L' | 'M' | 'Q' | 'H';
  copyShow?: boolean;
  copyText?: string;
  downloadShow?: boolean;
  downloadText?: string;
  downloadFilename?: string;
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  copyUrl: '',
  width: 100,
  level: 'M',
  copyShow: true,
  copyText: '复制链接地址',
  downloadShow: true,
  downloadText: '下载二维码图片',
  downloadFilename: '二维码.png',
});

async function handleCopy() {
  await clipboard(props.copyUrl || props.value);
}

function handleDownload() {
  const canvas = document.querySelector("#qr-code")?.querySelector("canvas");

  if (canvas) {
    const url = canvas.toDataURL();
    const a = document.createElement("a");
    a.download = props.downloadFilename;
    a.href = url;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }
}
</script>

<template>
  <NGrid :x-gap="6">
    <NGi :span="24" class="flex-center">
      <div class="pr-24px">
        <NQrCode id="qr-code" :value="value" :size="width" :error-correction-level="level" />
      </div>
    </NGi>
    <NGi :span="24" class="flex-center">
      <NSpace class="pt-4">
        <n-button v-if="copyShow" text tag="a" type="primary" @click="handleCopy">
          {{ copyText }}
        </n-button>
        <n-divider v-if="copyShow && downloadShow" vertical />
        <n-button v-if="downloadShow" text tag="a" type="primary" @click="handleDownload">
          {{ downloadText }}
        </n-button>
      </NSpace>
    </NGi>
  </NGrid>
</template>
